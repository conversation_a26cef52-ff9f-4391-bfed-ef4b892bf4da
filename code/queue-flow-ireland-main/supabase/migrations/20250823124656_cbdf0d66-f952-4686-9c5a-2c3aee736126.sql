-- Create businesses table
CREATE TABLE public.businesses (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('salon', 'clinic', 'restaurant', 'barbershop', 'dental', 'other')),
  phone TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'closed', 'busy', 'paused')),
  staff_count INTEGER NOT NULL DEFAULT 1 CHECK (staff_count > 0),
  avg_service_time INTEGER NOT NULL DEFAULT 15, -- minutes
  owner_id UUID,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create queues table for individual customers in queue
CREATE TABLE public.queues (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  customer_name TEXT NOT NULL,
  customer_phone TEXT NOT NULL,
  position INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'waiting' CHECK (status IN ('waiting', 'next', 'serving', 'completed', 'cancelled')),
  estimated_wait INTEGER, -- minutes
  joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  served_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  service_duration INTEGER, -- actual service time in minutes
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create analytics table for business metrics
CREATE TABLE public.analytics (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  total_customers INTEGER NOT NULL DEFAULT 0,
  avg_wait_time FLOAT,
  avg_service_time FLOAT,
  completion_rate FLOAT,
  peak_hour INTEGER, -- hour of day with most customers
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(business_id, date)
);

-- Enable Row Level Security
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.queues ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for businesses (allow all operations for now - will add auth later)
CREATE POLICY "Allow all operations on businesses" ON public.businesses FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on queues" ON public.queues FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on analytics" ON public.analytics FOR ALL USING (true) WITH CHECK (true);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_businesses_updated_at
  BEFORE UPDATE ON public.businesses
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_queues_updated_at
  BEFORE UPDATE ON public.queues
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_analytics_updated_at
  BEFORE UPDATE ON public.analytics
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_queues_business_id ON public.queues(business_id);
CREATE INDEX idx_queues_status ON public.queues(status);
CREATE INDEX idx_queues_position ON public.queues(business_id, position);
CREATE INDEX idx_analytics_business_date ON public.analytics(business_id, date);

-- Insert sample business data
INSERT INTO public.businesses (name, type, phone, status, staff_count, avg_service_time) VALUES
('Bella''s Hair Salon', 'salon', '+353871234567', 'open', 3, 45),
('Dublin Medical Clinic', 'clinic', '+353851234567', 'open', 2, 20),
('O''Connell Street Barber', 'barbershop', '+353861234567', 'open', 1, 25),
('Temple Bar Bistro', 'restaurant', '+353871234568', 'busy', 4, 15);

-- Insert sample queue data for testing
INSERT INTO public.queues (business_id, customer_name, customer_phone, position, status, estimated_wait) VALUES
((SELECT id FROM public.businesses WHERE name = 'Bella''s Hair Salon'), 'Sarah Murphy', '+353871111111', 1, 'next', 5),
((SELECT id FROM public.businesses WHERE name = 'Bella''s Hair Salon'), 'John O''Brien', '+353872222222', 2, 'waiting', 15),
((SELECT id FROM public.businesses WHERE name = 'Bella''s Hair Salon'), 'Emma Kelly', '+353873333333', 3, 'waiting', 25);