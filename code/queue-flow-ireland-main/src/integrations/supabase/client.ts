// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ocnukgjfqhvfserhuszu.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9jbnVrZ2pmcWh2ZnNlcmh1c3p1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU5NTI4NzMsImV4cCI6MjA3MTUyODg3M30.ZzXtdB3s-k3aplOHPjzvPLbWKuyJai7L0ILuhw8Ip10";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});