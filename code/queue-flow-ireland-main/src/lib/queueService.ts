import { supabase } from "@/integrations/supabase/client";
import { Business, QueueCustomer, Analytics, JoinQueueRequest, BusinessStats } from "./types";

// AI Wait Time Prediction Engine
export const calculateWaitTime = (
  business: Business,
  queuePosition: number,
  queueLength: number
): number => {
  // Base service time by business type
  const serviceTimeMultipliers = {
    salon: 1.0,        // 45 min base
    clinic: 0.44,      // 20 min base  
    restaurant: 0.33,  // 15 min base
    barbershop: 0.56,  // 25 min base
    dental: 0.67,      // 30 min base
    other: 0.67        // 30 min base
  };

  // Time of day factors
  const hour = new Date().getHours();
  let timeOfDayFactor = 1.0;
  if (hour >= 12 && hour <= 13) timeOfDayFactor = 1.5; // Lunch rush
  else if (hour >= 9 && hour <= 11) timeOfDayFactor = 1.3; // Morning rush
  else if (hour >= 17 && hour <= 19) timeOfDayFactor = 1.4; // Evening rush

  // Day of week factor
  const dayOfWeek = new Date().getDay();
  const weekendFactor = (dayOfWeek === 0 || dayOfWeek === 6) ? 1.2 : 1.0;

  // Queue position calculation
  const baseServiceTime = business.avg_service_time * serviceTimeMultipliers[business.type];
  const effectivePosition = Math.max(0, queuePosition - business.staff_count);
  
  // Calculate estimated wait time
  let estimatedWait = effectivePosition * baseServiceTime;
  estimatedWait *= timeOfDayFactor * weekendFactor;
  
  // Add buffer time (10-20% variance)
  const bufferMultiplier = 1.1 + (Math.random() * 0.1);
  estimatedWait *= bufferMultiplier;
  
  return Math.round(Math.max(5, estimatedWait)); // Minimum 5 minutes
};

// Business Operations
export const businessService = {
  async getAll(): Promise<Business[]> {
    const { data, error } = await supabase
      .from('businesses')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return (data || []) as Business[];
  },

  async getById(id: string): Promise<Business | null> {
    const { data, error } = await supabase
      .from('businesses')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data as Business;
  },

  async create(business: Omit<Business, 'id' | 'created_at' | 'updated_at'>): Promise<Business> {
    const { data, error } = await supabase
      .from('businesses')
      .insert(business)
      .select()
      .single();
    
    if (error) throw error;
    return data as Business;
  },

  async update(id: string, updates: Partial<Business>): Promise<Business> {
    const { data, error } = await supabase
      .from('businesses')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data as Business;
  },

  async updateStatus(id: string, status: Business['status']): Promise<Business> {
    return this.update(id, { status });
  }
};

// Queue Operations
export const queueService = {
  async getQueueByBusiness(businessId: string): Promise<QueueCustomer[]> {
    const { data, error } = await supabase
      .from('queues')
      .select('*')
      .eq('business_id', businessId)
      .in('status', ['waiting', 'next', 'serving'])
      .order('position');
    
    if (error) throw error;
    return (data || []) as QueueCustomer[];
  },

  async joinQueue(businessId: string, request: JoinQueueRequest): Promise<QueueCustomer> {
    // Get current queue to determine position
    const currentQueue = await this.getQueueByBusiness(businessId);
    const newPosition = currentQueue.length + 1;
    
    // Get business for wait time calculation
    const business = await businessService.getById(businessId);
    if (!business) throw new Error('Business not found');
    
    const estimatedWait = calculateWaitTime(business, newPosition, currentQueue.length);
    
    const { data, error } = await supabase
      .from('queues')
      .insert({
        business_id: businessId,
        customer_name: request.customer_name,
        customer_phone: request.customer_phone,
        position: newPosition,
        status: newPosition === 1 ? 'next' : 'waiting',
        estimated_wait: estimatedWait
      })
      .select()
      .single();
    
    if (error) throw error;
    return data as QueueCustomer;
  },

  async serveNext(businessId: string): Promise<QueueCustomer | null> {
    const queue = await this.getQueueByBusiness(businessId);
    const nextCustomer = queue.find(c => c.status === 'next');
    
    if (!nextCustomer) return null;
    
    // Update current customer to serving
    const { data: served, error: serveError } = await supabase
      .from('queues')
      .update({ 
        status: 'serving',
        served_at: new Date().toISOString()
      })
      .eq('id', nextCustomer.id)
      .select()
      .single();
    
    if (serveError) throw serveError;
    
    // Update queue positions and set next customer
    await this.updateQueuePositions(businessId);
    
    return served as QueueCustomer;
  },

  async completeService(customerId: string, serviceDuration?: number): Promise<QueueCustomer> {
    const { data, error } = await supabase
      .from('queues')
      .update({ 
        status: 'completed',
        completed_at: new Date().toISOString(),
        service_duration: serviceDuration
      })
      .eq('id', customerId)
      .select()
      .single();
    
    if (error) throw error;
    
    // Update queue positions after completion
    const customer = data as QueueCustomer;
    await this.updateQueuePositions(customer.business_id);
    
    return data as QueueCustomer;
  },

  async cancelCustomer(customerId: string): Promise<void> {
    const { data: customer, error: getError } = await supabase
      .from('queues')
      .select('business_id')
      .eq('id', customerId)
      .single();
    
    if (getError) throw getError;
    
    const { error } = await supabase
      .from('queues')
      .update({ status: 'cancelled' })
      .eq('id', customerId);
    
    if (error) throw error;
    
    // Update queue positions after cancellation
    await this.updateQueuePositions(customer.business_id);
  },

  async updateQueuePositions(businessId: string): Promise<void> {
    const queue = await this.getQueueByBusiness(businessId);
    const business = await businessService.getById(businessId);
    
    if (!business) return;
    
    // Recalculate positions and wait times
    for (let i = 0; i < queue.length; i++) {
      const customer = queue[i];
      const newPosition = i + 1;
      const newStatus = newPosition === 1 ? 'next' : 'waiting';
      const newWaitTime = calculateWaitTime(business, newPosition, queue.length);
      
      await supabase
        .from('queues')
        .update({
          position: newPosition,
          status: newStatus,
          estimated_wait: newWaitTime
        })
        .eq('id', customer.id);
    }
  },

  async getCustomerStatus(customerId: string): Promise<QueueCustomer | null> {
    const { data, error } = await supabase
      .from('queues')
      .select('*')
      .eq('id', customerId)
      .single();
    
    if (error) return null;
    return data as QueueCustomer;
  }
};

export const analyticsService = {
  // Get business statistics for today
  async getBusinessStats(businessId: string, date?: Date): Promise<BusinessStats> {
    const today = date || new Date();
    const dateStr = today.toISOString().split('T')[0];
    
    // Get today's queue data
    const { data: queueData, error: queueError } = await supabase
      .from('queues')
      .select('*')
      .eq('business_id', businessId)
      .gte('joined_at', `${dateStr}T00:00:00`)
      .lt('joined_at', `${dateStr}T23:59:59`);

    if (queueError) throw queueError;

    const totalCustomers = queueData?.length || 0;
    const completedCustomers = queueData?.filter(c => c.status === 'completed') || [];
    const waitTimes = queueData?.filter(c => c.served_at && c.joined_at)
      .map(c => {
        const joined = new Date(c.joined_at).getTime();
        const served = new Date(c.served_at!).getTime();
        return (served - joined) / (1000 * 60); // minutes
      }) || [];

    const avgWaitTime = waitTimes.length > 0 
      ? waitTimes.reduce((a, b) => a + b, 0) / waitTimes.length 
      : 0;

    const completionRate = totalCustomers > 0 
      ? (completedCustomers.length / totalCustomers) * 100 
      : 100;

    const queueEfficiency = totalCustomers > 0 
      ? Math.max(0, 100 - (avgWaitTime * 2)) // Simple efficiency calculation
      : 100;

    return {
      todayCustomers: totalCustomers,
      avgWaitTime: Math.round(avgWaitTime * 10) / 10,
      completionRate: Math.round(completionRate * 10) / 10,
      queueEfficiency: Math.round(queueEfficiency)
    };
  },

  // Update daily analytics with proper conflict handling
  async updateDailyAnalytics(businessId: string): Promise<void> {
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0];
    
    try {
      const stats = await this.getBusinessStats(businessId, today);
      
      // Find peak hour
      const { data: hourlyData, error: hourlyError } = await supabase
        .from('queues')
        .select('joined_at')
        .eq('business_id', businessId)
        .gte('joined_at', `${dateStr}T00:00:00`)
        .lt('joined_at', `${dateStr}T23:59:59`);

      if (hourlyError) throw hourlyError;

      const hourCounts = new Array(24).fill(0);
      hourlyData?.forEach(record => {
        const hour = new Date(record.joined_at).getHours();
        hourCounts[hour]++;
      });

      const peakHour = hourCounts.indexOf(Math.max(...hourCounts));

      // Check if record exists first
      const { data: existingRecord } = await supabase
        .from('analytics')
        .select('id')
        .eq('business_id', businessId)
        .eq('date', dateStr)
        .single();

      if (existingRecord) {
        // Update existing record
        const { error: updateError } = await supabase
          .from('analytics')
          .update({
            total_customers: stats.todayCustomers,
            avg_wait_time: stats.avgWaitTime,
            avg_service_time: 0,
            completion_rate: stats.completionRate,
            peak_hour: peakHour >= 0 ? peakHour : null,
            updated_at: new Date().toISOString()
          })
          .eq('business_id', businessId)
          .eq('date', dateStr);

        if (updateError) throw updateError;
      } else {
        // Insert new record
        const { error: insertError } = await supabase
          .from('analytics')
          .insert({
            business_id: businessId,
            date: dateStr,
            total_customers: stats.todayCustomers,
            avg_wait_time: stats.avgWaitTime,
            avg_service_time: 0,
            completion_rate: stats.completionRate,
            peak_hour: peakHour >= 0 ? peakHour : null
          });

        if (insertError) throw insertError;
      }
    } catch (error) {
      console.error('Failed to update analytics:', error);
      // Don't throw error to prevent blocking other operations
    }
  }
};

// Real-time subscriptions
export const subscribeToQueue = (
  businessId: string, 
  callback: (queue: QueueCustomer[]) => void
) => {
  return supabase
    .channel(`queue-${businessId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'queues',
        filter: `business_id=eq.${businessId}`
      },
      async () => {
        // Fetch updated queue when changes occur
        const queue = await queueService.getQueueByBusiness(businessId);
        callback(queue);
      }
    )
    .subscribe();
};