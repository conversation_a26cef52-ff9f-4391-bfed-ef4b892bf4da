export interface Business {
  id: string;
  name: string;
  type: 'salon' | 'clinic' | 'restaurant' | 'barbershop' | 'dental' | 'other';
  phone: string;
  status: 'open' | 'closed' | 'busy' | 'paused';
  staff_count: number;
  avg_service_time: number;
  owner_id?: string;
  created_at: string;
  updated_at: string;
}

export interface QueueCustomer {
  id: string;
  business_id: string;
  customer_name: string;
  customer_phone: string;
  position: number;
  status: 'waiting' | 'next' | 'serving' | 'completed' | 'cancelled';
  estimated_wait: number | null;
  joined_at: string;
  served_at: string | null;
  completed_at: string | null;
  service_duration: number | null;
  created_at: string;
  updated_at: string;
}

export interface Analytics {
  id: string;
  business_id: string;
  date: string;
  total_customers: number;
  avg_wait_time: number | null;
  avg_service_time: number | null;
  completion_rate: number | null;
  peak_hour: number | null;
  created_at: string;
  updated_at: string;
}

export interface JoinQueueRequest {
  customer_name: string;
  customer_phone: string;
}

export interface BusinessStats {
  todayCustomers: number;
  avgWaitTime: number;
  completionRate: number;
  queueEfficiency: number;
}