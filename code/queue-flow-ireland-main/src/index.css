@tailwind base;
@tailwind components;
@tailwind utilities;

/* QueueFlow Design System - Premium SaaS for Irish SMEs
All colors MUST be HSL for consistency across light/dark modes.
*/

@layer base {
  :root {
    /* Base colors - Clean, professional foundation */
    --background: 0 0% 100%;
    --foreground: 215 25% 17%;

    /* Card system - Subtle elevation */
    --card: 0 0% 100%;
    --card-foreground: 215 25% 17%;

    /* Interactive surfaces */
    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 17%;

    /* Primary brand - Emerald green for trust & growth */
    --primary: 155 84% 44%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 155 84% 38%;
    --primary-light: 155 84% 90%;

    /* Secondary palette - Professional gray-blue */
    --secondary: 217 19% 27%;
    --secondary-foreground: 0 0% 100%;
    --secondary-light: 217 19% 96%;

    /* Muted surfaces - Subtle backgrounds */
    --muted: 215 20% 97%;
    --muted-foreground: 215 16% 47%;

    /* Accent - Warm orange for CTAs and highlights */
    --accent: 25 100% 62%;
    --accent-foreground: 0 0% 100%;
    --accent-light: 25 100% 95%;

    /* Status colors */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Border and input system */
    --border: 215 20% 90%;
    --input: 215 20% 90%;
    --ring: 155 84% 44%;

    /* Premium gradients */
    --gradient-primary: linear-gradient(135deg, hsl(155 84% 44%) 0%, hsl(155 84% 38%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(217 19% 27%) 0%, hsl(217 19% 20%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(25 100% 62%) 0%, hsl(25 100% 55%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(155 84% 44%) 0%, hsl(174 87% 51%) 50%, hsl(25 100% 62%) 100%);
    --gradient-subtle: linear-gradient(180deg, hsl(0 0% 100%) 0%, hsl(215 20% 98%) 100%);

    /* Shadows - Elegant depth */
    --shadow-sm: 0 1px 2px 0 hsl(215 25% 17% / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(215 25% 17% / 0.1), 0 2px 4px -1px hsl(215 25% 17% / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(215 25% 17% / 0.1), 0 4px 6px -2px hsl(215 25% 17% / 0.05);
    --shadow-xl: 0 20px 25px -5px hsl(215 25% 17% / 0.1), 0 10px 10px -5px hsl(215 25% 17% / 0.04);
    --shadow-glow: 0 0 40px hsl(155 84% 44% / 0.3);

    /* Animation system - Enhanced for premium interactions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-bounce: 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Interactive effects */
    --hover-scale: 1.05;
    --hover-lift: -8px;
    --active-scale: 0.95;
    
    /* Queue-specific colors */
    --queue-waiting: 38 92% 50%;
    --queue-next: 155 84% 44%;
    --queue-serving: 25 100% 62%;
    --queue-completed: 142 71% 45%;
    --queue-cancelled: 0 84% 60%;

    --radius: 0.75rem;
  }

  .dark {
    /* Dark mode - Professional and accessible */
    --background: 215 25% 9%;
    --foreground: 0 0% 98%;

    --card: 215 25% 12%;
    --card-foreground: 0 0% 98%;

    --popover: 215 25% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 155 84% 48%;
    --primary-foreground: 0 0% 9%;
    --primary-hover: 155 84% 42%;
    --primary-light: 155 84% 15%;

    --secondary: 215 25% 18%;
    --secondary-foreground: 0 0% 98%;
    --secondary-light: 215 25% 25%;

    --accent: 25 100% 65%;
    --accent-foreground: 0 0% 9%;
    --accent-light: 25 100% 15%;

    --success: 142 71% 48%;
    --success-foreground: 0 0% 9%;
    --warning: 38 92% 55%;
    --warning-foreground: 0 0% 9%;
    --destructive: 0 84% 65%;
    --destructive-foreground: 0 0% 98%;

    --muted: 215 25% 15%;
    --muted-foreground: 215 20% 65%;

    --border: 215 25% 18%;
    --input: 215 25% 18%;
    --ring: 155 84% 48%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(155 84% 48%) 0%, hsl(155 84% 42%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(215 25% 18%) 0%, hsl(215 25% 12%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(25 100% 65%) 0%, hsl(25 100% 58%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(155 84% 48%) 0%, hsl(174 87% 54%) 50%, hsl(25 100% 65%) 100%);
    --gradient-subtle: linear-gradient(180deg, hsl(215 25% 9%) 0%, hsl(215 25% 12%) 100%);

    /* Dark mode shadows */
    --shadow-glow: 0 0 40px hsl(155 84% 48% / 0.4);
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom utility classes for QueueFlow */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-hero bg-clip-text text-transparent;
  }
  
  .btn-primary {
    @apply bg-gradient-primary text-primary-foreground shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-normal;
  }
  
  .btn-secondary {
    @apply bg-gradient-secondary text-secondary-foreground shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-normal;
  }
  
  .btn-accent {
    @apply bg-gradient-accent text-accent-foreground shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-normal;
  }
  
  .card-hover {
    @apply transition-all duration-normal hover:shadow-xl hover:-translate-y-1;
  }
  
  .section-padding {
    @apply py-16 md:py-24 lg:py-32;
  }
  
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* Premium interactive effects */
  .interactive-hover {
    @apply transition-all duration-300 hover:scale-105 hover:-translate-y-2 hover:shadow-xl cursor-pointer;
  }

  .glass-effect {
    @apply backdrop-blur-lg bg-background/80 border border-border/50;
  }

  .floating-animation {
    animation: float 3s ease-in-out infinite;
  }

  .pulse-primary {
    animation: pulse-primary 2s infinite;
  }

  .drag-handle {
    @apply cursor-grab hover:cursor-grabbing transition-colors hover:text-primary;
  }

  .drag-preview {
    @apply rotate-3 scale-105 shadow-2xl opacity-90;
  }

  /* Queue status colors */
  .status-waiting { @apply bg-warning/10 text-warning border-warning/20; }
  .status-next { @apply bg-primary/10 text-primary border-primary/20; }
  .status-serving { @apply bg-accent/10 text-accent border-accent/20; }
  .status-completed { @apply bg-success/10 text-success border-success/20; }
  .status-cancelled { @apply bg-destructive/10 text-destructive border-destructive/20; }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse-primary {
  0%, 100% { box-shadow: 0 0 0 0 hsl(var(--primary) / 0.4); }
  50% { box-shadow: 0 0 0 20px hsl(var(--primary) / 0); }
}