import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Building2, 
  Users, 
  TrendingUp, 
  Clock, 
  AlertTriangle,
  CheckCircle2,
  BarChart3,
  Settings,
  Eye,
  Plus
} from "lucide-react";
import { motion } from "framer-motion";
import { Business, BusinessStats } from "@/lib/types";
import { businessService, analyticsService } from "@/lib/queueService";
import { useToast } from "@/hooks/use-toast";

interface BusinessOverview extends Business {
  stats: BusinessStats;
  healthScore: number;
  alertsCount: number;
}

const AdminDashboard = () => {
  const [businesses, setBusinesses] = useState<BusinessOverview[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBusiness, setSelectedBusiness] = useState<BusinessOverview | null>(null);
  
  const { toast } = useToast();

  useEffect(() => {
    loadBusinessesOverview();
    
    // Update every 30 seconds
    const interval = setInterval(loadBusinessesOverview, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadBusinessesOverview = async () => {
    try {
      const businessList = await businessService.getAll();
      
      const businessOverviews = await Promise.all(
        businessList.map(async (business) => {
          try {
            const stats = await analyticsService.getBusinessStats(business.id);
            const healthScore = calculateHealthScore(business, stats);
            const alertsCount = getAlertsCount(business, stats);
            
            return {
              ...business,
              stats,
              healthScore,
              alertsCount
            } as BusinessOverview;
          } catch (error) {
            // Return business with default stats if analytics fail
            return {
              ...business,
              stats: { todayCustomers: 0, avgWaitTime: 0, completionRate: 100, queueEfficiency: 100 },
              healthScore: 50,
              alertsCount: 0
            } as BusinessOverview;
          }
        })
      );

      setBusinesses(businessOverviews);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load businesses overview",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateHealthScore = (business: Business, stats: BusinessStats): number => {
    let score = 100;
    
    // Penalize high wait times
    if (stats.avgWaitTime > 15) score -= 20;
    else if (stats.avgWaitTime > 10) score -= 10;
    
    // Penalize low completion rate
    if (stats.completionRate < 80) score -= 15;
    else if (stats.completionRate < 90) score -= 8;
    
    // Penalize low efficiency
    if (stats.queueEfficiency < 70) score -= 15;
    else if (stats.queueEfficiency < 85) score -= 8;
    
    // Consider business status
    if (business.status === 'paused') score -= 10;
    if (business.status === 'closed') score -= 20;
    
    return Math.max(0, score);
  };

  const getAlertsCount = (business: Business, stats: BusinessStats): number => {
    let alerts = 0;
    
    if (stats.avgWaitTime > 15) alerts++;
    if (stats.completionRate < 80) alerts++;
    if (stats.queueEfficiency < 70) alerts++;
    if (business.status === 'paused') alerts++;
    
    return alerts;
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return "text-success";
    if (score >= 70) return "text-primary";
    if (score >= 50) return "text-warning";
    return "text-destructive";
  };

  const getHealthScoreBadge = (score: number) => {
    if (score >= 90) return { label: "Excellent", variant: "default" as const };
    if (score >= 70) return { label: "Good", variant: "secondary" as const };
    if (score >= 50) return { label: "Fair", variant: "outline" as const };
    return { label: "Needs Attention", variant: "destructive" as const };
  };

  const totalCustomersToday = businesses.reduce((sum, b) => sum + b.stats.todayCustomers, 0);
  const avgWaitTime = businesses.length > 0 
    ? businesses.reduce((sum, b) => sum + b.stats.avgWaitTime, 0) / businesses.length 
    : 0;
  const activeBusinesses = businesses.filter(b => b.status === 'open').length;
  const totalAlerts = businesses.reduce((sum, b) => sum + b.alertsCount, 0);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="animate-pulse h-24 bg-muted rounded-lg"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <div key={i} className="animate-pulse h-48 bg-muted rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Admin Overview Metrics */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid grid-cols-1 md:grid-cols-4 gap-4"
      >
        <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Building2 className="h-5 w-5 text-primary" />
              <div>
                <div className="text-2xl font-bold text-primary">{businesses.length}</div>
                <div className="text-sm text-muted-foreground">Total Businesses</div>
                <div className="text-xs text-success">{activeBusinesses} active</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-success/10 to-success/5 border-success/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Users className="h-5 w-5 text-success" />
              <div>
                <div className="text-2xl font-bold text-success">{totalCustomersToday}</div>
                <div className="text-sm text-muted-foreground">Customers Today</div>
                <div className="text-xs text-muted-foreground">Across all businesses</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-accent/10 to-accent/5 border-accent/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-accent" />
              <div>
                <div className="text-2xl font-bold text-accent">{avgWaitTime.toFixed(1)}m</div>
                <div className="text-sm text-muted-foreground">Avg Wait Time</div>
                <div className="text-xs text-muted-foreground">Network average</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className={`bg-gradient-to-r border-2 ${
          totalAlerts > 0 
            ? 'from-destructive/10 to-destructive/5 border-destructive/20' 
            : 'from-success/10 to-success/5 border-success/20'
        }`}>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              {totalAlerts > 0 ? (
                <AlertTriangle className="h-5 w-5 text-destructive" />
              ) : (
                <CheckCircle2 className="h-5 w-5 text-success" />
              )}
              <div>
                <div className={`text-2xl font-bold ${totalAlerts > 0 ? 'text-destructive' : 'text-success'}`}>
                  {totalAlerts}
                </div>
                <div className="text-sm text-muted-foreground">Active Alerts</div>
                <div className="text-xs text-muted-foreground">
                  {totalAlerts === 0 ? 'All systems healthy' : 'Need attention'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Business Overview Grid */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Business Network Overview
            </CardTitle>
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Business
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {businesses.map((business, index) => (
              <motion.div
                key={business.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer border-l-4 border-l-primary">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-lg">{business.name}</h3>
                        <p className="text-sm text-muted-foreground capitalize">{business.type}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant={getHealthScoreBadge(business.healthScore).variant}
                          className="text-xs"
                        >
                          {getHealthScoreBadge(business.healthScore).label}
                        </Badge>
                        {business.alertsCount > 0 && (
                          <Badge variant="destructive" className="text-xs">
                            {business.alertsCount} alerts
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* Health Score */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Health Score</span>
                      <span className={`font-bold ${getHealthScoreColor(business.healthScore)}`}>
                        {business.healthScore}%
                      </span>
                    </div>

                    {/* Key Metrics */}
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="text-center p-2 bg-muted/50 rounded">
                        <div className="font-bold text-primary">{business.stats.todayCustomers}</div>
                        <div className="text-muted-foreground text-xs">Customers</div>
                      </div>
                      <div className="text-center p-2 bg-muted/50 rounded">
                        <div className="font-bold text-accent">{business.stats.avgWaitTime.toFixed(1)}m</div>
                        <div className="text-muted-foreground text-xs">Avg Wait</div>
                      </div>
                    </div>

                    {/* Status and Actions */}
                    <div className="flex items-center justify-between pt-2 border-t">
                      <Badge 
                        variant="outline" 
                        className={`flex items-center gap-1 ${
                          business.status === 'open' ? 'text-success border-success' :
                          business.status === 'busy' ? 'text-warning border-warning' :
                          business.status === 'paused' ? 'text-muted border-muted' :
                          'text-destructive border-destructive'
                        }`}
                      >
                        <div className={`w-2 h-2 rounded-full ${
                          business.status === 'open' ? 'bg-success' :
                          business.status === 'busy' ? 'bg-warning' :
                          business.status === 'paused' ? 'bg-muted' :
                          'bg-destructive'
                        } animate-pulse`}></div>
                        <span className="capitalize">{business.status}</span>
                      </Badge>
                      
                      <div className="flex gap-1">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => setSelectedBusiness(business)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* System Health Alerts */}
      {totalAlerts > 0 && (
        <Card className="border-destructive/20 bg-destructive/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              System Health Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {businesses.filter(b => b.alertsCount > 0).map(business => (
                <div key={business.id} className="flex items-center justify-between p-3 bg-background rounded-lg border">
                  <div>
                    <div className="font-medium">{business.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {business.stats.avgWaitTime > 15 && "High wait times • "}
                      {business.stats.completionRate < 80 && "Low completion rate • "}
                      {business.stats.queueEfficiency < 70 && "Poor efficiency • "}
                      {business.status === 'paused' && "Queue paused • "}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive">
                      {business.alertsCount} alerts
                    </Badge>
                    <Button variant="outline" size="sm">
                      Investigate
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdminDashboard;