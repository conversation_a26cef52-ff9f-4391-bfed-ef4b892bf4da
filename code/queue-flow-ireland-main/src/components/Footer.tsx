import { MessageSquare, Mail, Phone, MapPin } from "lucide-react";
import { Badge } from "@/components/ui/badge";

const Footer = () => {
  const footerSections = [
    {
      title: "Product",
      links: [
        { label: "Features", href: "#features" },
        { label: "Pricing", href: "#pricing" },
        { label: "API Documentation", href: "#" },
        { label: "Integrations", href: "#" }
      ]
    },
    {
      title: "Company", 
      links: [
        { label: "About Us", href: "#" },
        { label: "Careers", href: "#" },
        { label: "Blog", href: "#" },
        { label: "Press Kit", href: "#" }
      ]
    },
    {
      title: "Support",
      links: [
        { label: "Help Center", href: "#" },
        { label: "Contact Support", href: "#" },
        { label: "GDPR Compliance", href: "#" },
        { label: "System Status", href: "#" }
      ]
    },
    {
      title: "Legal",
      links: [
        { label: "Privacy Policy", href: "#" },
        { label: "Terms of Service", href: "#" },
        { label: "Cookie Policy", href: "#" },
        { label: "Data Protection", href: "#" }
      ]
    }
  ];

  return (
    <footer className="bg-secondary text-secondary-foreground">
      <div className="container mx-auto container-padding">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
            {/* Company Info */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <div className="bg-gradient-primary p-2 rounded-lg">
                  <MessageSquare className="h-6 w-6 text-primary-foreground" />
                </div>
                <div>
                  <span className="text-2xl font-bold text-secondary-foreground">QueueFlow</span>
                  <Badge variant="secondary" className="ml-2 text-xs bg-primary/20 text-primary">
                    🇮🇪 Made in Ireland
                  </Badge>
                </div>
              </div>
              
              <p className="text-lg text-secondary-foreground/80 max-w-md">
                AI-native virtual queue management for Irish SMEs. 
                Simple, secure, and GDPR compliant.
              </p>
              
              <div className="space-y-3">
                <div className="flex items-center space-x-3 text-secondary-foreground/80">
                  <Mail className="h-5 w-5" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3 text-secondary-foreground/80">
                  <Phone className="h-5 w-5" />
                  <span>+353 1 234 5678</span>
                </div>
                <div className="flex items-center space-x-3 text-secondary-foreground/80">
                  <MapPin className="h-5 w-5" />
                  <span>Dublin, Ireland</span>
                </div>
              </div>
            </div>

            {/* Footer Links */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {footerSections.map((section, index) => (
                <div key={index}>
                  <h3 className="font-semibold text-secondary-foreground mb-4">
                    {section.title}
                  </h3>
                  <ul className="space-y-3">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a
                          href={link.href}
                          className="text-secondary-foreground/70 hover:text-secondary-foreground transition-colors duration-normal"
                        >
                          {link.label}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Irish Business Focus */}
          <div className="bg-primary/10 rounded-lg p-6 mb-8">
            <h3 className="font-semibold text-secondary-foreground mb-3">
              Supporting Irish Business Growth 🇮🇪
            </h3>
            <p className="text-secondary-foreground/80 mb-4">
              QueueFlow is proudly Irish-built and designed specifically for the needs of Irish SMEs. 
              We understand local business challenges and provide GDPR-compliant solutions with Irish data residency.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>GDPR Compliant</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Irish Data Residency</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Local Support Team</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-secondary-foreground/20 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <p className="text-secondary-foreground/60 text-sm">
              © 2025 QueueFlow Ireland Ltd. All rights reserved.
            </p>
            <div className="flex items-center space-x-6 text-sm text-secondary-foreground/60">
              <span>Built with ❤️ in Dublin</span>
              <span>•</span>
              <span>Serving Irish SMEs nationwide</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;