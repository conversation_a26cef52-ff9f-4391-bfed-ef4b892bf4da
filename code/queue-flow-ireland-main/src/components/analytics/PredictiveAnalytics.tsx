import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Brain,
  Target,
  Zap
} from 'lucide-react';
import { Business, BusinessStats } from '@/lib/types';

interface PredictiveAnalyticsProps {
  business: Business;
  stats: BusinessStats;
}

export const PredictiveAnalytics: React.FC<PredictiveAnalyticsProps> = ({
  business,
  stats
}) => {
  const predictions = {
    noShowProbability: 15,
    peakHourPrediction: '2:00 PM - 4:00 PM',
    demandForecast: 'High',
    recommendedStaffing: business.staff_count + 1,
    revenueImpact: '+€127 daily',
    customerSatisfaction: 94
  };

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-4"
      >
        <Card className="bg-gradient-primary text-primary-foreground">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Brain className="h-5 w-5" />
              <div>
                <p className="text-sm opacity-90">AI Confidence</p>
                <p className="text-2xl font-bold">96%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-accent" />
              <div>
                <p className="text-sm text-muted-foreground">Revenue Impact</p>
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-4 w-4 text-success" />
                  <p className="text-2xl font-bold text-success">{predictions.revenueImpact}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-warning" />
              <div>
                <p className="text-sm text-muted-foreground">No-Show Risk</p>
                <p className="text-2xl font-bold">{predictions.noShowProbability}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Smart Recommendations</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Customer Satisfaction Score</span>
              <div className="flex items-center space-x-2">
                <Progress value={predictions.customerSatisfaction} className="w-20" />
                <span className="font-medium">{predictions.customerSatisfaction}%</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span>Predicted Peak Hours</span>
              <Badge variant="outline">{predictions.peakHourPrediction}</Badge>
            </div>

            <div className="flex items-center justify-between">
              <span>Recommended Staffing</span>
              <Badge className="bg-accent text-accent-foreground">
                {predictions.recommendedStaffing} staff members
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};