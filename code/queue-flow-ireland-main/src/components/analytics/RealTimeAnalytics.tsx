import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  Clock, 
  TrendingUp, 
  CheckCircle,
  Calendar,
  BarChart3,
  Activity,
  Zap
} from "lucide-react";
import { motion } from "framer-motion";
import { Business, BusinessStats, QueueCustomer } from "@/lib/types";
import { analyticsService, subscribeToQueue } from "@/lib/queueService";

interface RealTimeAnalyticsProps {
  business: Business;
  queue?: QueueCustomer[];
}

const RealTimeAnalytics = ({ business, queue = [] }: RealTimeAnalyticsProps) => {
  const [stats, setStats] = useState<BusinessStats>({
    todayCustomers: 0,
    avgWaitTime: 0,
    completionRate: 0,
    queueEfficiency: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    loadStats();
    
    // Set up real-time subscription for instant updates
    const subscription = subscribeToQueue(business.id, () => {
      loadStats();
      setLastUpdate(new Date());
    });

    // Auto-refresh every 15 seconds
    const interval = setInterval(loadStats, 15000);
    
    return () => {
      subscription.unsubscribe();
      clearInterval(interval);
    };
  }, [business.id]);

  const loadStats = async () => {
    try {
      const businessStats = await analyticsService.getBusinessStats(business.id);
      setStats(businessStats);
      
      // Update daily analytics in background
      await analyticsService.updateDailyAnalytics(business.id);
    } catch (error) {
      console.error('Failed to load real-time analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-success';
      case 'busy': return 'bg-warning';
      case 'paused': return 'bg-muted';
      case 'closed': return 'bg-destructive';
      default: return 'bg-muted';
    }
  };

  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 90) return 'text-success';
    if (efficiency >= 75) return 'text-primary';
    if (efficiency >= 60) return 'text-warning';
    return 'text-destructive';
  };

  const getCompletionColor = (rate: number) => {
    if (rate >= 90) return 'text-success';
    if (rate >= 75) return 'text-primary';
    if (rate >= 60) return 'text-warning';
    return 'text-destructive';
  };

  // Real-time metrics from current queue state
  const currentQueueLength = queue.length;
  const servingNow = queue.filter(c => c.status === 'serving').length;
  const avgEstimatedWait = queue.length > 0 
    ? Math.round(queue.reduce((sum, c) => sum + (c.estimated_wait || 0), 0) / queue.length)
    : 0;

  const analyticsData = [
    {
      title: "Today's Customers",
      value: stats.todayCustomers.toString(),
      realTimeValue: `+${currentQueueLength} in queue`,
      change: stats.todayCustomers > 0 ? `+${stats.todayCustomers}` : "0",
      changeType: "positive" as const,
      icon: Users,
      color: "text-primary"
    },
    {
      title: "Current Queue",
      value: currentQueueLength.toString(),
      realTimeValue: servingNow > 0 ? `${servingNow} being served` : "No one being served",
      change: currentQueueLength > 5 ? "High volume" : currentQueueLength > 0 ? "Active" : "Empty",
      changeType: currentQueueLength > 5 ? "neutral" : currentQueueLength > 0 ? "positive" : "neutral",
      icon: Activity,
      color: "text-accent"
    },
    {
      title: "Average Wait Time",
      value: `${stats.avgWaitTime.toFixed(1)} min`,
      realTimeValue: `${avgEstimatedWait} min current`,
      change: stats.avgWaitTime < 5 ? "Excellent" : stats.avgWaitTime < 10 ? "Good" : "Needs improvement",
      changeType: stats.avgWaitTime < 10 ? "positive" : "negative",
      icon: Clock,
      color: "text-warning"
    },
    {
      title: "Queue Efficiency",
      value: `${stats.queueEfficiency}%`,
      realTimeValue: "Live tracking",
      change: stats.queueEfficiency >= 90 ? "Optimal" : stats.queueEfficiency >= 75 ? "Good" : "Fair",
      changeType: stats.queueEfficiency >= 75 ? "positive" : "neutral",
      icon: Zap,
      color: getEfficiencyColor(stats.queueEfficiency)
    }
  ];

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map(i => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-muted rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Real-time Status Header */}
      <Card className="bg-gradient-to-r from-primary/5 to-accent/5">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold mb-2 flex items-center gap-2">
                <Activity className="h-6 w-6 text-primary animate-pulse" />
                {business.name} - Live Analytics
              </h3>
              <p className="text-muted-foreground flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {new Date().toLocaleDateString('en-IE', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
                <span>•</span>
                Last updated: {lastUpdate.toLocaleTimeString('en-IE', { 
                  hour: '2-digit', 
                  minute: '2-digit',
                  second: '2-digit'
                })}
              </p>
            </div>
            <div className="flex items-center gap-4">
              <Badge 
                variant="outline" 
                className="flex items-center gap-2"
              >
                <div className={`w-2 h-2 rounded-full ${getStatusColor(business.status)} animate-pulse`}></div>
                {formatStatus(business.status)}
              </Badge>
              
              <div className="text-right">
                <div className="text-sm text-muted-foreground">Staff Count</div>
                <div className="font-semibold">{business.staff_count}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Real-time Analytics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {analyticsData.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card 
              className="border-0 shadow-lg bg-background/80 backdrop-blur-sm hover:shadow-xl transition-all duration-200"
            >
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <stat.icon className={`h-5 w-5 ${stat.color}`} />
                  <Badge 
                    variant={stat.changeType === 'positive' ? 'default' : stat.changeType === 'negative' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {stat.change}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold mb-1 ${stat.color}`}>
                  {stat.value}
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {stat.title}
                </p>
                <div className="text-xs text-accent font-medium">
                  {stat.realTimeValue}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Live Queue Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Live Queue Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {currentQueueLength > 0 ? (
              <>
                <div className="flex items-center justify-between p-4 bg-primary/10 rounded-lg">
                  <div>
                    <div className="font-semibold">Current Queue Status</div>
                    <div className="text-sm text-muted-foreground">
                      {currentQueueLength} customers waiting • {servingNow} being served
                    </div>
                  </div>
                  <Badge className="bg-primary text-primary-foreground">
                    {avgEstimatedWait} min avg wait
                  </Badge>
                </div>

                {queue.some(c => c.status === 'next') && (
                  <div className="flex items-center justify-between p-4 bg-accent/10 rounded-lg">
                    <div>
                      <div className="font-semibold">Next Customer Ready</div>
                      <div className="text-sm text-muted-foreground">
                        {queue.find(c => c.status === 'next')?.customer_name} is ready to be served
                      </div>
                    </div>
                    <Badge className="bg-accent text-accent-foreground animate-pulse">
                      Action Required
                    </Badge>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-6 text-muted-foreground">
                <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No customers in queue</p>
                <p className="text-sm">Analytics will update as customers join</p>
              </div>
            )}

            <div className="grid grid-cols-3 gap-4 text-center pt-4 border-t">
              <div>
                <div className="text-lg font-bold text-primary">{stats.todayCustomers}</div>
                <div className="text-xs text-muted-foreground">Total Today</div>
              </div>
              <div>
                <div className="text-lg font-bold text-success">
                  {stats.completionRate.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">Completion Rate</div>
              </div>
              <div>
                <div className="text-lg font-bold text-accent">
                  {business.avg_service_time}m
                </div>
                <div className="text-xs text-muted-foreground">Avg Service Time</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RealTimeAnalytics;