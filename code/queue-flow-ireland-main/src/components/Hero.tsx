import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowR<PERSON>, Check, Clock, MessageSquare, Users } from "lucide-react";
import heroImage from "@/assets/hero-dashboard.jpg";

const Hero = () => {
  return (
    <section className="section-padding bg-gradient-subtle">
      <div className="container mx-auto container-padding">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Content */}
            <div className="animate-fade-in-up">
              <Badge variant="secondary" className="mb-6 text-sm font-medium">
                🇮🇪 Made for Irish SMEs
              </Badge>
              
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Smart queues,{" "}
                <span className="text-gradient">happy customers</span>,{" "}
                instant setup
              </h1>
              
              <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                The first queue management system that works entirely through WhatsApp and SMS. 
                No app downloads, no hardware, no technical setup required.
              </p>
              
              {/* Key Benefits */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center space-x-3">
                  <div className="bg-primary/10 p-2 rounded-lg">
                    <Clock className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-foreground font-medium">5-minute setup</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="bg-primary/10 p-2 rounded-lg">
                    <MessageSquare className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-foreground font-medium">WhatsApp & SMS</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="bg-primary/10 p-2 rounded-lg">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-foreground font-medium">AI wait predictions</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="bg-primary/10 p-2 rounded-lg">
                    <Check className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-foreground font-medium">GDPR compliant</span>
                </div>
              </div>
              
              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="btn-primary text-lg px-8 py-4">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button variant="outline" size="lg" className="text-lg px-8 py-4">
                  Watch Demo
                </Button>
              </div>
              
              {/* Social Proof */}
              <div className="mt-8 pt-8 border-t border-border">
                <p className="text-sm text-muted-foreground mb-4">Trusted by Irish businesses</p>
                <div className="flex items-center space-x-8">
                  <div className="text-2xl font-bold text-primary">500+</div>
                  <div className="text-sm text-muted-foreground">
                    Businesses<br />signed up
                  </div>
                  <div className="text-2xl font-bold text-primary">€2.4K</div>
                  <div className="text-sm text-muted-foreground">
                    Average annual<br />savings
                  </div>
                  <div className="text-2xl font-bold text-primary">4.9★</div>
                  <div className="text-sm text-muted-foreground">
                    Customer<br />rating
                  </div>
                </div>
              </div>
            </div>
            
            {/* Right Column - Hero Image */}
            <div className="animate-scale-in">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-primary rounded-2xl blur-2xl opacity-20 animate-pulse-glow"></div>
                <img
                  src={heroImage}
                  alt="QueueFlow Dashboard - Modern queue management interface"
                  className="relative rounded-2xl shadow-2xl w-full h-auto"
                />
                {/* Floating Elements */}
                <div className="absolute -top-4 -right-4 bg-success text-success-foreground px-4 py-2 rounded-lg shadow-lg animate-bounce-gentle">
                  <div className="text-sm font-medium">Live Queue</div>
                  <div className="text-xs opacity-90">3 customers waiting</div>
                </div>
                <div className="absolute -bottom-4 -left-4 bg-accent text-accent-foreground px-4 py-2 rounded-lg shadow-lg animate-bounce-gentle">
                  <div className="text-sm font-medium">AI Prediction</div>
                  <div className="text-xs opacity-90">~7 min wait</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;