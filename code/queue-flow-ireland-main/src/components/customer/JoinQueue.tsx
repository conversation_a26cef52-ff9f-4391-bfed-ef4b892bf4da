import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Clock, Users, Phone, CheckCircle, AlertTriangle } from "lucide-react";
import { Business, QueueCustomer, JoinQueueRequest } from "@/lib/types";
import { businessService, queueService } from "@/lib/queueService";
import { useToast } from "@/hooks/use-toast";

const JoinQueue = () => {
  const { businessId } = useParams<{ businessId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [business, setBusiness] = useState<Business | null>(null);
  const [queueLength, setQueueLength] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isJoining, setIsJoining] = useState(false);
  const [customerData, setCustomerData] = useState<JoinQueueRequest>({
    customer_name: "",
    customer_phone: ""
  });

  useEffect(() => {
    if (businessId) {
      loadBusinessData();
    }
  }, [businessId]);

  const loadBusinessData = async () => {
    if (!businessId) return;
    
    try {
      const [businessData, queueData] = await Promise.all([
        businessService.getById(businessId),
        queueService.getQueueByBusiness(businessId)
      ]);
      
      setBusiness(businessData);
      setQueueLength(queueData.length);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load business information",
        variant: "destructive"
      });
      navigate('/');
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinQueue = async () => {
    if (!business || !customerData.customer_name || !customerData.customer_phone) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive"
      });
      return;
    }

    if (business.status !== 'open') {
      toast({
        title: "Queue Unavailable",
        description: `Sorry, ${business.name} is currently ${business.status}`,
        variant: "destructive"
      });
      return;
    }

    setIsJoining(true);
    try {
      const customer = await queueService.joinQueue(business.id, customerData);
      
      toast({
        title: "Successfully Joined Queue!",
        description: `You are #${customer.position} in line. Estimated wait: ~${customer.estimated_wait} minutes`
      });
      
      // Redirect to queue status page
      navigate(`/queue/status/${customer.id}`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to join queue. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsJoining(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-pulse space-y-4 w-full max-w-md">
          <div className="h-8 bg-muted rounded"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!business) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-destructive" />
            <h2 className="text-xl font-semibold mb-2">Business Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The business you're looking for doesn't exist or is no longer available.
            </p>
            <Button onClick={() => navigate('/')}>
              Go Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-success text-success-foreground';
      case 'busy': return 'bg-warning text-warning-foreground';
      case 'paused': return 'bg-muted text-muted-foreground';
      case 'closed': return 'bg-destructive text-destructive-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const isBusinessOpen = business.status === 'open';

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl mb-2">{business.name}</CardTitle>
          <Badge className={`mx-auto ${getStatusColor(business.status)}`}>
            {business.status.charAt(0).toUpperCase() + business.status.slice(1)}
          </Badge>
          <p className="text-sm text-muted-foreground mt-2">
            {business.type.charAt(0).toUpperCase() + business.type.slice(1)}
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Queue Info */}
          <div className="bg-muted/30 p-4 rounded-lg space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">Current Queue</span>
              </div>
              <Badge variant="outline">
                {queueLength} {queueLength === 1 ? 'person' : 'people'} waiting
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-primary" />
              <span className="text-sm">
                Average service time: {business.avg_service_time} minutes
              </span>
            </div>
          </div>

          {isBusinessOpen ? (
            <>
              <Separator />
              
              {/* Join Queue Form */}
              <div className="space-y-4">
                <h3 className="font-semibold">Join the Queue</h3>
                
                <div className="space-y-2">
                  <Label htmlFor="name">Your Name *</Label>
                  <Input
                    id="name"
                    value={customerData.customer_name}
                    onChange={(e) => setCustomerData(prev => ({ 
                      ...prev, 
                      customer_name: e.target.value 
                    }))}
                    placeholder="e.g., John Smith"
                    disabled={isJoining}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={customerData.customer_phone}
                    onChange={(e) => setCustomerData(prev => ({ 
                      ...prev, 
                      customer_phone: e.target.value 
                    }))}
                    placeholder="+353 87 123 4567"
                    disabled={isJoining}
                  />
                  <p className="text-xs text-muted-foreground">
                    We'll send you updates about your queue position
                  </p>
                </div>

                <Button 
                  onClick={handleJoinQueue}
                  disabled={isJoining || !customerData.customer_name || !customerData.customer_phone}
                  className="w-full btn-primary"
                >
                  {isJoining ? (
                    "Joining Queue..."
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Join Queue
                    </>
                  )}
                </Button>
              </div>

              <div className="text-xs text-center text-muted-foreground">
                By joining the queue, you agree to provide accurate contact information
              </div>
            </>
          ) : (
            <>
              <Separator />
              
              <div className="text-center py-6">
                <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-warning" />
                <h3 className="font-semibold mb-2">Queue Currently {business.status}</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {business.status === 'closed' ? 
                    `${business.name} is currently closed. Please try again during business hours.` :
                    business.status === 'paused' ?
                    `${business.name} has temporarily paused their queue. Please try again in a few minutes.` :
                    `${business.name} is currently busy and not accepting new customers.`
                  }
                </p>
                <Button variant="outline" onClick={() => navigate('/')}>
                  Back to Home
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default JoinQueue;