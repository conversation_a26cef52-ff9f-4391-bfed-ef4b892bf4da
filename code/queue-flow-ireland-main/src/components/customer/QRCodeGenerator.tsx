import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { QrCode, Download, Share, Smartphone, Monitor } from "lucide-react";
import { Business } from "@/lib/types";

interface QRCodeGeneratorProps {
  business: Business;
}

const QRCodeGenerator = ({ business }: QRCodeGeneratorProps) => {
  const [selectedInterface, setSelectedInterface] = useState<'standard' | 'whatsapp'>('standard');
  
  // Generate QR code URLs for different interfaces
  const baseUrl = window.location.origin;
  const standardUrl = `${baseUrl}/queue/join/${business.id}`;
  const whatsappUrl = `${baseUrl}/queue/whatsapp/${business.id}`;
  
  // QR Code API (using qr-server.com - free service)
  const generateQRCodeUrl = (data: string, size = 200) => {
    return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(data)}&bgcolor=FFFFFF&color=000000&margin=10`;
  };

  const handleDownload = async (url: string, filename: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Failed to download QR code:', error);
    }
  };

  const handleShare = async (url: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Join Queue - ${business.name}`,
          text: `Skip the line at ${business.name}! Use this QR code to join the queue.`,
          url: url
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(url);
    }
  };

  const currentUrl = selectedInterface === 'standard' ? standardUrl : whatsappUrl;
  const qrCodeUrl = generateQRCodeUrl(currentUrl, 300);

  return (
    <div className="space-y-6">
      {/* Interface Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            QR Code Generator
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Generate QR codes for customers to join your queue instantly
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant={selectedInterface === 'standard' ? 'default' : 'outline'}
              onClick={() => setSelectedInterface('standard')}
              className="h-auto p-4 flex flex-col items-center gap-2"
            >
              <Monitor className="h-6 w-6" />
              <div className="text-center">
                <div className="font-semibold">Standard Web Interface</div>
                <div className="text-xs text-muted-foreground">
                  Clean web form for joining queue
                </div>
              </div>
            </Button>
            
            <Button
              variant={selectedInterface === 'whatsapp' ? 'default' : 'outline'}
              onClick={() => setSelectedInterface('whatsapp')}
              className="h-auto p-4 flex flex-col items-center gap-2"
            >
              <Smartphone className="h-6 w-6" />
              <div className="text-center">
                <div className="font-semibold">WhatsApp-Style Interface</div>
                <div className="text-xs text-muted-foreground">
                  Familiar chat interface for customers
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* QR Code Display */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* QR Code */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              {selectedInterface === 'standard' ? 'Standard Interface' : 'WhatsApp Interface'} QR Code
            </CardTitle>
            <Badge variant="outline" className="w-fit">
              {business.type.charAt(0).toUpperCase() + business.type.slice(1)}
            </Badge>
          </CardHeader>
          <CardContent className="text-center">
            <div className="bg-white p-6 rounded-lg inline-block mb-6 shadow-sm">
              <img 
                src={qrCodeUrl} 
                alt={`QR Code for ${business.name}`}
                className="w-64 h-64 mx-auto"
              />
            </div>
            
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {selectedInterface === 'standard' 
                  ? 'Customers scan this code to access the standard web interface'
                  : 'Customers scan this code to access the WhatsApp-style chat interface'
                }
              </p>
              
              <div className="flex flex-col sm:flex-row gap-2 justify-center">
                <Button 
                  onClick={() => handleDownload(qrCodeUrl, `${business.name}-qr-${selectedInterface}.png`)}
                  variant="outline"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button 
                  onClick={() => handleShare(currentUrl)}
                  variant="outline"
                >
                  <Share className="h-4 w-4 mr-2" />
                  Share Link
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">How to Use</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex gap-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                  1
                </div>
                <div>
                  <div className="font-semibold">Display the QR Code</div>
                  <div className="text-sm text-muted-foreground">
                    Print or display the QR code at your business entrance
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                  2
                </div>
                <div>
                  <div className="font-semibold">Customers Scan</div>
                  <div className="text-sm text-muted-foreground">
                    Customers use their phone camera to scan the QR code
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                  3
                </div>
                <div>
                  <div className="font-semibold">Join Queue Instantly</div>
                  <div className="text-sm text-muted-foreground">
                    They enter their details and get their queue position
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                  4
                </div>
                <div>
                  <div className="font-semibold">Real-time Updates</div>
                  <div className="text-sm text-muted-foreground">
                    They receive live updates about their position and wait time
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-4 border-t border-border">
              <div className="bg-muted/30 p-4 rounded-lg">
                <div className="text-sm font-semibold mb-2">📱 Pro Tip</div>
                <div className="text-sm text-muted-foreground">
                  Place the QR code at eye level near your entrance. Add a simple sign: 
                  "Skip the wait - Scan to join our digital queue!"
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Link Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Direct Link</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/30 p-3 rounded-md text-sm font-mono break-all">
            {currentUrl}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QRCodeGenerator;