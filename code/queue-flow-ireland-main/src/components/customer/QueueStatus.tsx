import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Clock, 
  Users, 
  CheckCircle, 
  AlertTriangle, 
  Phone,
  Calendar,
  X
} from "lucide-react";
import { QueueCustomer, Business } from "@/lib/types";
import { queueService, businessService } from "@/lib/queueService";
import { useToast } from "@/hooks/use-toast";

const QueueStatus = () => {
  const { customerId } = useParams<{ customerId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [customer, setCustomer] = useState<QueueCustomer | null>(null);
  const [business, setBusiness] = useState<Business | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCancelling, setIsCancelling] = useState(false);

  useEffect(() => {
    if (customerId) {
      loadCustomerStatus();
      
      // Poll for updates every 10 seconds
      const interval = setInterval(loadCustomerStatus, 10000);
      return () => clearInterval(interval);
    }
  }, [customerId]);

  const loadCustomerStatus = async () => {
    if (!customerId) return;
    
    try {
      const customerData = await queueService.getCustomerStatus(customerId);
      
      if (!customerData) {
        toast({
          title: "Customer Not Found",
          description: "This queue position is no longer valid",
          variant: "destructive"
        });
        navigate('/');
        return;
      }
      
      setCustomer(customerData);
      
      // Load business info if not already loaded
      if (!business) {
        const businessData = await businessService.getById(customerData.business_id);
        setBusiness(businessData);
      }
    } catch (error) {
      console.error('Failed to load customer status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelQueue = async () => {
    if (!customer) return;
    
    setIsCancelling(true);
    try {
      await queueService.cancelCustomer(customer.id);
      toast({
        title: "Queue Position Cancelled",
        description: "You have been removed from the queue"
      });
      navigate('/');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to cancel queue position",
        variant: "destructive"
      });
    } finally {
      setIsCancelling(false);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-IE', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getStatusInfo = (status: QueueCustomer['status']) => {
    switch (status) {
      case 'next':
        return {
          color: 'bg-primary text-primary-foreground',
          message: 'You\'re next! Please be ready.',
          icon: Users
        };
      case 'serving':
        return {
          color: 'bg-accent text-accent-foreground',
          message: 'You are currently being served',
          icon: CheckCircle
        };
      case 'waiting':
        return {
          color: 'bg-muted text-muted-foreground',
          message: 'Please wait for your turn',
          icon: Clock
        };
      case 'completed':
        return {
          color: 'bg-success text-success-foreground',
          message: 'Service completed. Thank you!',
          icon: CheckCircle
        };
      case 'cancelled':
        return {
          color: 'bg-destructive text-destructive-foreground',
          message: 'Your queue position was cancelled',
          icon: X
        };
      default:
        return {
          color: 'bg-muted text-muted-foreground',
          message: 'Status unknown',
          icon: AlertTriangle
        };
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-pulse space-y-4 w-full max-w-md">
          <div className="h-8 bg-muted rounded"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!customer || !business) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-destructive" />
            <h2 className="text-xl font-semibold mb-2">Queue Position Not Found</h2>
            <p className="text-muted-foreground mb-4">
              This queue position doesn't exist or has been removed.
            </p>
            <Button onClick={() => navigate('/')}>
              Go Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const statusInfo = getStatusInfo(customer.status);
  const StatusIcon = statusInfo.icon;
  const isActive = ['waiting', 'next', 'serving'].includes(customer.status);

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-xl mb-2">{business.name}</CardTitle>
          <Badge className={statusInfo.color}>
            <StatusIcon className="h-3 w-3 mr-1" />
            {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
          </Badge>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Status Message */}
          <div className="text-center">
            <StatusIcon className="h-16 w-16 mx-auto mb-4 text-primary" />
            <p className="text-lg font-semibold mb-2">
              {statusInfo.message}
            </p>
          </div>

          <Separator />

          {/* Queue Information */}
          <div className="space-y-4">
            <h3 className="font-semibold">Queue Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold text-primary">
                  #{customer.position}
                </div>
                <div className="text-xs text-muted-foreground">
                  Position in Queue
                </div>
              </div>
              
              <div className="text-center p-3 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold text-primary">
                  ~{customer.estimated_wait}
                </div>
                <div className="text-xs text-muted-foreground">
                  Minutes Wait
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Customer Name:</span>
                <span className="font-medium">{customer.customer_name}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Phone:</span>
                <span className="font-medium">{customer.customer_phone}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Joined at:</span>
                <span className="font-medium">{formatTime(customer.joined_at)}</span>
              </div>

              {customer.served_at && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Served at:</span>
                  <span className="font-medium">{formatTime(customer.served_at)}</span>
                </div>
              )}

              {customer.completed_at && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Completed at:</span>
                  <span className="font-medium">{formatTime(customer.completed_at)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {isActive && (
              <>
                <Button 
                  variant="destructive" 
                  className="w-full"
                  onClick={handleCancelQueue}
                  disabled={isCancelling}
                >
                  <X className="h-4 w-4 mr-2" />
                  {isCancelling ? 'Cancelling...' : 'Cancel Queue Position'}
                </Button>
                
                <p className="text-xs text-center text-muted-foreground">
                  Your position will update automatically. 
                  Please keep this page open to receive updates.
                </p>
              </>
            )}

            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => navigate('/')}
            >
              Back to Home
            </Button>
          </div>

          {/* Auto-refresh indicator */}
          {isActive && (
            <div className="text-center">
              <div className="inline-flex items-center gap-2 text-xs text-muted-foreground">
                <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                Auto-updating every 10 seconds
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default QueueStatus;