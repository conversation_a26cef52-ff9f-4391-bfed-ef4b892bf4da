import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  UserCheck, 
  Clock, 
  Phone, 
  X, 
  UserPlus, 
  Play, 
  Pause,
  MoreHorizontal 
} from "lucide-react";
import { QueueCustomer, Business, JoinQueueRequest } from "@/lib/types";
import { queueService, subscribeToQueue, businessService } from "@/lib/queueService";
import { useToast } from "@/hooks/use-toast";

interface QueueManagerProps {
  business: Business;
}

const QueueManager = ({ business }: QueueManagerProps) => {
  const [queue, setQueue] = useState<QueueCustomer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [newCustomer, setNewCustomer] = useState<JoinQueueRequest>({
    customer_name: "",
    customer_phone: ""
  });
  
  const { toast } = useToast();

  useEffect(() => {
    loadQueue();
    
    // Set up real-time subscription
    const subscription = subscribeToQueue(business.id, (updatedQueue) => {
      setQueue(updatedQueue);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [business.id]);

  const loadQueue = async () => {
    try {
      const data = await queueService.getQueueByBusiness(business.id);
      setQueue(data);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load queue",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddWalkIn = async () => {
    if (!newCustomer.customer_name || !newCustomer.customer_phone) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await queueService.joinQueue(business.id, newCustomer);
      setIsAddDialogOpen(false);
      setNewCustomer({ customer_name: "", customer_phone: "" });
      toast({
        title: "Success",
        description: `${newCustomer.customer_name} added to queue`
      });
    } catch (error) {
      toast({
        title: "Error", 
        description: "Failed to add customer to queue",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleServeNext = async () => {
    setIsProcessing(true);
    try {
      const served = await queueService.serveNext(business.id);
      if (served) {
        toast({
          title: "Success",
          description: `Now serving ${served.customer_name}`
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to serve next customer",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCompleteService = async (customerId: string, customerName: string) => {
    setIsProcessing(true);
    try {
      await queueService.completeService(customerId);
      toast({
        title: "Success",
        description: `Service completed for ${customerName}`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to complete service",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelCustomer = async (customerId: string, customerName: string) => {
    setIsProcessing(true);
    try {
      await queueService.cancelCustomer(customerId);
      toast({
        title: "Success",
        description: `${customerName} removed from queue`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove customer",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePauseQueue = async () => {
    setIsProcessing(true);
    try {
      const newStatus = business.status === 'paused' ? 'open' : 'paused';
      await businessService.updateStatus(business.id, newStatus);
      toast({
        title: "Success",
        description: `Queue ${newStatus === 'paused' ? 'paused' : 'resumed'}`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update queue status",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSendUpdate = async () => {
    setIsProcessing(true);
    try {
      // Simulate sending WhatsApp/SMS updates to all customers
      const waitingCustomers = queue.filter(c => c.status === 'waiting');
      
      for (const customer of waitingCustomers) {
        // In real implementation, would send actual notifications
        console.log(`Sending update to ${customer.customer_name}: Your estimated wait time is ${customer.estimated_wait} minutes`);
      }
      
      toast({
        title: "Updates Sent",
        description: `Sent wait time updates to ${waitingCustomers.length} customers`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send updates",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClearCompleted = async () => {
    setIsProcessing(true);
    try {
      const completedCustomers = queue.filter(c => c.status === 'completed');
      
      // In a real implementation, you might archive instead of delete
      for (const customer of completedCustomers) {
        await queueService.cancelCustomer(customer.id);
      }
      
      toast({
        title: "Success",
        description: `Cleared ${completedCustomers.length} completed customers`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clear completed customers",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-IE', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getStatusBadge = (status: QueueCustomer['status']) => {
    switch (status) {
      case 'next':
        return <Badge className="bg-primary text-primary-foreground">Next</Badge>;
      case 'serving':
        return <Badge className="bg-accent text-accent-foreground">Being Served</Badge>;
      case 'waiting':
        return <Badge variant="secondary">Waiting</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isLoading) {
    return <div className="animate-pulse space-y-4">
      {[1, 2, 3].map(i => (
        <div key={i} className="h-16 bg-muted rounded-lg"></div>
      ))}
    </div>;
  }

  const nextCustomer = queue.find(c => c.status === 'next');
  const servingCustomer = queue.find(c => c.status === 'serving');

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl">Current Queue</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-primary">
              {queue.length} customers waiting
            </Badge>
            
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Walk-in
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Walk-in Customer</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="customer-name">Customer Name *</Label>
                    <Input
                      id="customer-name"
                      value={newCustomer.customer_name}
                      onChange={(e) => setNewCustomer(prev => ({ 
                        ...prev, 
                        customer_name: e.target.value 
                      }))}
                      placeholder="e.g., John Smith"
                    />
                  </div>
                  <div>
                    <Label htmlFor="customer-phone">Phone Number *</Label>
                    <Input
                      id="customer-phone"
                      value={newCustomer.customer_phone}
                      onChange={(e) => setNewCustomer(prev => ({ 
                        ...prev, 
                        customer_phone: e.target.value 
                      }))}
                      placeholder="+353 87 123 4567"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button 
                      variant="outline" 
                      onClick={() => setIsAddDialogOpen(false)}
                      disabled={isProcessing}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleAddWalkIn} disabled={isProcessing}>
                      {isProcessing ? "Adding..." : "Add to Queue"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {queue.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <UserPlus className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No customers in queue</p>
            <p className="text-sm">Add a walk-in customer or share your QR code</p>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Currently Being Served */}
            {servingCustomer && (
              <div className="p-4 rounded-lg border-2 border-accent bg-accent/10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-accent text-accent-foreground font-bold">
                      <UserCheck className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="font-semibold">{servingCustomer.customer_name}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-2">
                        <Phone className="h-3 w-3" />
                        {servingCustomer.customer_phone}
                        <span>•</span>
                        <Clock className="h-3 w-3" />
                        Started {formatTime(servingCustomer.served_at!)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {getStatusBadge(servingCustomer.status)}
                    <Button 
                      onClick={() => handleCompleteService(servingCustomer.id, servingCustomer.customer_name)}
                      disabled={isProcessing}
                      className="btn-primary"
                    >
                      Complete Service
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Queue List */}
            {queue.filter(c => c.status !== 'serving').map((customer) => (
              <div 
                key={customer.id}
                className={`flex items-center justify-between p-4 rounded-lg border transition-all duration-normal ${
                  customer.status === 'next' 
                    ? 'bg-primary/10 border-primary/30 shadow-md' 
                    : 'bg-muted/30 border-border hover:bg-muted/50'
                }`}
              >
                <div className="flex items-center gap-4">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full font-bold ${
                    customer.status === 'next'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    {customer.position}
                  </div>
                  <div>
                    <div className="font-semibold">{customer.customer_name}</div>
                    <div className="text-sm text-muted-foreground flex items-center gap-2">
                      <Phone className="h-3 w-3" />
                      {customer.customer_phone}
                      <span>•</span>
                      <Clock className="h-3 w-3" />
                      Joined {formatTime(customer.joined_at)}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="font-medium text-sm">
                      ~{customer.estimated_wait} min
                    </div>
                    <div className="text-xs text-muted-foreground">
                      estimated wait
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {getStatusBadge(customer.status)}
                    
                    {customer.status === 'next' && (
                      <Button 
                        onClick={handleServeNext}
                        disabled={isProcessing}
                        className="btn-primary"
                      >
                        <UserCheck className="h-4 w-4 mr-2" />
                        Serve Now
                      </Button>
                    )}
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleCancelCustomer(customer.id, customer.customer_name)}
                      disabled={isProcessing}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-6 pt-6 border-t border-border">
          <div className="flex flex-wrap gap-3">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handlePauseQueue}
              disabled={isProcessing}
            >
              <Pause className="h-4 w-4 mr-2" />
              {business.status === 'paused' ? 'Resume Queue' : 'Pause Queue'}
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleSendUpdate}
              disabled={isProcessing || queue.length === 0}
            >
              <MoreHorizontal className="h-4 w-4 mr-2" />
              Send Update to All
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleClearCompleted}
              disabled={isProcessing}
            >
              Clear Completed
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QueueManager;