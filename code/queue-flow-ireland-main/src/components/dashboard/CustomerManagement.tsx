import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Users, 
  Search, 
  Filter,
  Phone,
  Clock,
  Calendar,
  Star,
  MessageSquare,
  Edit3,
  Trash2,
  UserPlus
} from "lucide-react";
import { QueueCustomer, Business } from "@/lib/types";
import { queueService } from "@/lib/queueService";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface CustomerManagementProps {
  business: Business;
}

interface CustomerHistory extends QueueCustomer {
  visit_count?: number;
  last_visit?: string;
  total_wait_time?: number;
  avg_rating?: number;
}

const CustomerManagement = ({ business }: CustomerManagementProps) => {
  const [customers, setCustomers] = useState<CustomerHistory[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<CustomerHistory[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState<string>("today");
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadCustomers();
  }, [business.id, dateFilter]);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchTerm, statusFilter]);

  const loadCustomers = async () => {
    setIsLoading(true);
    try {
      let query = supabase
        .from('queues')
        .select('*')
        .eq('business_id', business.id)
        .order('joined_at', { ascending: false });

      // Apply date filter
      const today = new Date();
      if (dateFilter === 'today') {
        const dateStr = today.toISOString().split('T')[0];
        query = query
          .gte('joined_at', `${dateStr}T00:00:00`)
          .lt('joined_at', `${dateStr}T23:59:59`);
      } else if (dateFilter === 'week') {
        const weekAgo = new Date(today);
        weekAgo.setDate(today.getDate() - 7);
        query = query.gte('joined_at', weekAgo.toISOString());
      } else if (dateFilter === 'month') {
        const monthAgo = new Date(today);
        monthAgo.setMonth(today.getMonth() - 1);
        query = query.gte('joined_at', monthAgo.toISOString());
      }

      const { data, error } = await query.limit(100);

      if (error) throw error;

      // Enhance with customer history data
      const enhancedCustomers: CustomerHistory[] = data?.map(customer => {
        // In real implementation, this would aggregate data from multiple visits
        const waitTime = customer.served_at && customer.joined_at
          ? (new Date(customer.served_at).getTime() - new Date(customer.joined_at).getTime()) / (1000 * 60)
          : 0;

        return {
          ...customer,
          status: customer.status as QueueCustomer['status'], // Type assertion for database string
          visit_count: 1, // Would be calculated from historical data
          total_wait_time: waitTime,
          avg_rating: Math.random() * 2 + 3 // Would come from feedback system
        };
      }) || [];

      setCustomers(enhancedCustomers);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load customer data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterCustomers = () => {
    let filtered = customers;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(customer =>
        customer.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.customer_phone.includes(searchTerm)
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(customer => customer.status === statusFilter);
    }

    setFilteredCustomers(filtered);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-success text-success-foreground';
      case 'serving': return 'bg-accent text-accent-foreground';
      case 'waiting': return 'bg-primary text-primary-foreground';
      case 'next': return 'bg-warning text-warning-foreground';
      case 'cancelled': return 'bg-destructive text-destructive-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-IE', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IE');
  };

  const sendMessage = async (customer: CustomerHistory) => {
    try {
      // In real implementation, integrate with WhatsApp/SMS API
      console.log(`Sending message to ${customer.customer_name} at ${customer.customer_phone}`);
      toast({
        title: "Message Sent",
        description: `Message sent to ${customer.customer_name}`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive"
      });
    }
  };

  const removeCustomer = async (customerId: string, customerName: string) => {
    try {
      await queueService.cancelCustomer(customerId);
      toast({
        title: "Success",
        description: `${customerName} removed from records`
      });
      loadCustomers();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove customer",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="h-20 bg-muted rounded-lg animate-pulse"></div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <div>
                <div className="text-2xl font-bold">{customers.length}</div>
                <div className="text-sm text-muted-foreground">Total Customers</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-accent" />
              <div>
                <div className="text-2xl font-bold">
                  {Math.round(customers.reduce((acc, c) => acc + (c.total_wait_time || 0), 0) / customers.length || 0)}
                </div>
                <div className="text-sm text-muted-foreground">Avg Wait (min)</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-warning" />
              <div>
                <div className="text-2xl font-bold">
                  {(customers.reduce((acc, c) => acc + (c.avg_rating || 0), 0) / customers.length || 0).toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">Avg Rating</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <UserPlus className="h-5 w-5 text-success" />
              <div>
                <div className="text-2xl font-bold">
                  {customers.filter(c => c.status === 'completed').length}
                </div>
                <div className="text-sm text-muted-foreground">Completed</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Customers</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or phone number..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="waiting">Waiting</SelectItem>
                  <SelectItem value="serving">Serving</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Date Range</Label>
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customer List */}
      <Card>
        <CardContent className="p-0">
          {filteredCustomers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No customers found</p>
              <p className="text-sm">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredCustomers.map((customer) => (
                <div 
                  key={customer.id}
                  className="flex items-center justify-between p-4 border-b border-border last:border-b-0 hover:bg-muted/30 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <Users className="h-5 w-5 text-primary" />
                    </div>
                    
                    <div>
                      <div className="font-semibold">{customer.customer_name}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-4">
                        <span className="flex items-center gap-1">
                          <Phone className="h-3 w-3" />
                          {customer.customer_phone}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(customer.joined_at)}
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {formatTime(customer.joined_at)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right text-sm">
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 text-warning" />
                        <span>{customer.avg_rating?.toFixed(1) || 'N/A'}</span>
                      </div>
                      <div className="text-muted-foreground">
                        {customer.total_wait_time ? `${Math.round(customer.total_wait_time)}min wait` : 'No wait data'}
                      </div>
                    </div>
                    
                    <Badge className={getStatusColor(customer.status)}>
                      {customer.status}
                    </Badge>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => sendMessage(customer)}
                      >
                        <MessageSquare className="h-4 w-4" />
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeCustomer(customer.id, customer.customer_name)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerManagement;