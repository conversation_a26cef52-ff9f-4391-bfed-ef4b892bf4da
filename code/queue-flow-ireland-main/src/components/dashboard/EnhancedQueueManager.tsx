import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { 
  UserCheck, 
  Clock, 
  Phone, 
  X, 
  UserPlus, 
  Play, 
  Pause,
  MoreHorizontal,
  Timer,
  Zap,
  CheckCircle2,
  AlertCircle,
  TrendingUp,
  Users
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { QueueCustomer, Business, JoinQueueRequest } from "@/lib/types";
import { queueService, subscribeToQueue, businessService, analyticsService } from "@/lib/queueService";
import { useToast } from "@/hooks/use-toast";

interface EnhancedQueueManagerProps {
  business: Business;
  onAnalyticsUpdate?: () => void;
}

interface WorkflowState {
  isServing: boolean;
  servingCustomer: QueueCustomer | null;
  serviceStartTime: Date | null;
}

const EnhancedQueueManager = ({ business, onAnalyticsUpdate }: EnhancedQueueManagerProps) => {
  const [queue, setQueue] = useState<QueueCustomer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [workflowState, setWorkflowState] = useState<WorkflowState>({
    isServing: false,
    servingCustomer: null,
    serviceStartTime: null
  });
  const [serviceTimer, setServiceTimer] = useState(0);
  const [newCustomer, setNewCustomer] = useState<JoinQueueRequest>({
    customer_name: "",
    customer_phone: ""
  });
  
  const { toast } = useToast();

  // Timer for service duration
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (workflowState.isServing && workflowState.serviceStartTime) {
      interval = setInterval(() => {
        const elapsed = Math.floor((Date.now() - workflowState.serviceStartTime!.getTime()) / 1000);
        setServiceTimer(elapsed);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [workflowState.isServing, workflowState.serviceStartTime]);

  useEffect(() => {
    loadQueue();
    
    // Set up real-time subscription with analytics sync
    const subscription = subscribeToQueue(business.id, async (updatedQueue) => {
      setQueue(updatedQueue);
      // Update workflow state
      const serving = updatedQueue.find(c => c.status === 'serving');
      if (serving && !workflowState.isServing) {
        setWorkflowState({
          isServing: true,
          servingCustomer: serving,
          serviceStartTime: new Date(serving.served_at!)
        });
      } else if (!serving && workflowState.isServing) {
        setWorkflowState({
          isServing: false,
          servingCustomer: null,
          serviceStartTime: null
        });
        setServiceTimer(0);
      }
      
      // Trigger analytics update
      await analyticsService.updateDailyAnalytics(business.id);
      onAnalyticsUpdate?.();
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [business.id]);

  const loadQueue = async () => {
    try {
      const data = await queueService.getQueueByBusiness(business.id);
      setQueue(data);
      
      // Initialize workflow state
      const serving = data.find(c => c.status === 'serving');
      if (serving) {
        setWorkflowState({
          isServing: true,
          servingCustomer: serving,
          serviceStartTime: new Date(serving.served_at!)
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load queue",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleServeCustomer = async (customer: QueueCustomer) => {
    setIsProcessing(true);
    try {
      const served = await queueService.serveNext(business.id);
      if (served) {
        // Show workflow transition
        toast({
          title: "✨ Now Serving",
          description: (
            <div className="flex items-center gap-2">
              <UserCheck className="h-4 w-4 text-success" />
              <span>{served.customer_name} - Service started!</span>
            </div>
          )
        });

        // Update all remaining customers' wait times
        setTimeout(() => {
          toast({
            title: "🔄 Wait Times Updated",
            description: `Updated estimated wait times for ${queue.length - 1} remaining customers`,
            variant: "default"
          });
        }, 1000);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to serve customer",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCompleteService = async (customerId: string, customerName: string) => {
    setIsProcessing(true);
    try {
      const serviceDuration = Math.floor(serviceTimer / 60); // Convert to minutes
      await queueService.completeService(customerId, serviceDuration);
      
      toast({
        title: "🎉 Service Completed",
        description: (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-success" />
              <span>{customerName} - Service completed!</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Duration: {Math.floor(serviceTimer / 60)}m {serviceTimer % 60}s
            </div>
          </div>
        )
      });

      // Reset workflow state
      setWorkflowState({
        isServing: false,
        servingCustomer: null,
        serviceStartTime: null
      });
      setServiceTimer(0);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to complete service",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleAddWalkIn = async () => {
    if (!newCustomer.customer_name || !newCustomer.customer_phone) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      const addedCustomer = await queueService.joinQueue(business.id, newCustomer);
      setIsAddDialogOpen(false);
      setNewCustomer({ customer_name: "", customer_phone: "" });
      
      toast({
        title: "✅ Customer Added",
        description: (
          <div className="space-y-1">
            <div>{newCustomer.customer_name} added to position #{addedCustomer.position}</div>
            <div className="text-sm text-muted-foreground">
              Estimated wait: ~{addedCustomer.estimated_wait} minutes
            </div>
          </div>
        )
      });
    } catch (error) {
      toast({
        title: "Error", 
        description: "Failed to add customer to queue",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatServiceTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    if (!workflowState.serviceStartTime) return 0;
    const avgServiceTime = business.avg_service_time * 60; // Convert to seconds
    return Math.min((serviceTimer / avgServiceTime) * 100, 100);
  };

  const getStatusBadge = (status: QueueCustomer['status']) => {
    switch (status) {
      case 'next':
        return <Badge className="bg-primary text-primary-foreground animate-pulse">⚡ Next Up</Badge>;
      case 'serving':
        return <Badge className="bg-accent text-accent-foreground">🔥 Being Served</Badge>;
      case 'waiting':
        return <Badge variant="secondary">⏳ Waiting</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isLoading) {
    return <div className="animate-pulse space-y-4">
      {[1, 2, 3].map(i => (
        <div key={i} className="h-16 bg-muted rounded-lg"></div>
      ))}
    </div>;
  }

  const nextCustomer = queue.find(c => c.status === 'next');
  const servingCustomer = queue.find(c => c.status === 'serving');
  const waitingCustomers = queue.filter(c => c.status === 'waiting');

  return (
    <div className="space-y-6">
      {/* Performance Metrics Bar */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid grid-cols-4 gap-4"
      >
        <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-primary" />
              <div>
                <div className="text-2xl font-bold text-primary">{queue.length}</div>
                <div className="text-xs text-muted-foreground">In Queue</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-accent/10 to-accent/5 border-accent/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Timer className="h-4 w-4 text-accent" />
              <div>
                <div className="text-2xl font-bold text-accent">
                  {workflowState.isServing ? formatServiceTime(serviceTimer) : "0:00"}
                </div>
                <div className="text-xs text-muted-foreground">Current Service</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-success/10 to-success/5 border-success/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-success" />
              <div>
                <div className="text-2xl font-bold text-success">
                  {queue.length > 0 ? Math.round(queue.reduce((sum, c) => sum + (c.estimated_wait || 0), 0) / queue.length) : 0}m
                </div>
                <div className="text-xs text-muted-foreground">Avg Wait Time</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-warning/10 to-warning/5 border-warning/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-warning" />
              <div>
                <div className="text-2xl font-bold text-warning">
                  {workflowState.isServing ? Math.round(getProgressPercentage()) : 0}%
                </div>
                <div className="text-xs text-muted-foreground">Service Progress</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Main Queue Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl flex items-center gap-2">
              <Users className="h-5 w-5" />
              Live Queue Management
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-primary">
                {queue.length} customers
              </Badge>
              
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add Walk-in
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Walk-in Customer</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="customer-name">Customer Name *</Label>
                      <Input
                        id="customer-name"
                        value={newCustomer.customer_name}
                        onChange={(e) => setNewCustomer(prev => ({ 
                          ...prev, 
                          customer_name: e.target.value 
                        }))}
                        placeholder="e.g., John Smith"
                      />
                    </div>
                    <div>
                      <Label htmlFor="customer-phone">Phone Number *</Label>
                      <Input
                        id="customer-phone"
                        value={newCustomer.customer_phone}
                        onChange={(e) => setNewCustomer(prev => ({ 
                          ...prev, 
                          customer_phone: e.target.value 
                        }))}
                        placeholder="+353 87 123 4567"
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button 
                        variant="outline" 
                        onClick={() => setIsAddDialogOpen(false)}
                        disabled={isProcessing}
                      >
                        Cancel
                      </Button>
                      <Button onClick={handleAddWalkIn} disabled={isProcessing}>
                        {isProcessing ? "Adding..." : "Add to Queue"}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <AnimatePresence mode="popLayout">
            {queue.length === 0 ? (
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-8 text-muted-foreground"
              >
                <UserPlus className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No customers in queue</p>
                <p className="text-sm">Add a walk-in customer or share your QR code</p>
              </motion.div>
            ) : (
              <div className="space-y-3">
                {/* Currently Being Served */}
                <AnimatePresence>
                  {servingCustomer && (
                    <motion.div
                      initial={{ scale: 0.95, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.95, opacity: 0 }}
                      className="p-4 rounded-lg border-2 border-accent bg-gradient-to-r from-accent/20 to-accent/10"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-accent text-accent-foreground font-bold">
                            <UserCheck className="h-6 w-6" />
                          </div>
                          <div>
                            <div className="font-semibold text-lg">{servingCustomer.customer_name}</div>
                            <div className="text-sm text-muted-foreground flex items-center gap-2">
                              <Phone className="h-3 w-3" />
                              {servingCustomer.customer_phone}
                              <span>•</span>
                              <Timer className="h-3 w-3" />
                              Started {new Date(servingCustomer.served_at!).toLocaleTimeString('en-IE', { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                              })}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <div className="text-2xl font-bold text-accent">
                              {formatServiceTime(serviceTimer)}
                            </div>
                            <div className="text-xs text-muted-foreground">Service Time</div>
                          </div>
                          {getStatusBadge(servingCustomer.status)}
                        </div>
                      </div>

                      {/* Service Progress Bar */}
                      <div className="mb-3">
                        <div className="flex justify-between text-xs text-muted-foreground mb-1">
                          <span>Service Progress</span>
                          <span>{Math.round(getProgressPercentage())}%</span>
                        </div>
                        <Progress value={getProgressPercentage()} className="h-2" />
                        <div className="text-xs text-muted-foreground mt-1">
                          Expected duration: {business.avg_service_time} minutes
                        </div>
                      </div>

                      <Button 
                        onClick={() => handleCompleteService(servingCustomer.id, servingCustomer.customer_name)}
                        disabled={isProcessing}
                        className="w-full bg-success text-success-foreground hover:bg-success/90"
                      >
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                        Complete Service ({formatServiceTime(serviceTimer)})
                      </Button>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Next Customer */}
                <AnimatePresence>
                  {nextCustomer && (
                    <motion.div
                      initial={{ scale: 0.95, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.95, opacity: 0 }}
                      className="p-4 rounded-lg border-2 border-primary bg-gradient-to-r from-primary/20 to-primary/10 shadow-lg"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-primary-foreground font-bold animate-pulse">
                            {nextCustomer.position}
                          </div>
                          <div>
                            <div className="font-semibold text-lg">{nextCustomer.customer_name}</div>
                            <div className="text-sm text-muted-foreground flex items-center gap-2">
                              <Phone className="h-3 w-3" />
                              {nextCustomer.customer_phone}
                              <span>•</span>
                              <Clock className="h-3 w-3" />
                              Waiting {Math.floor((Date.now() - new Date(nextCustomer.joined_at).getTime()) / (1000 * 60))} min
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <div className="text-sm font-medium text-primary">Ready to serve!</div>
                            <div className="text-xs text-muted-foreground">Position #{nextCustomer.position}</div>
                          </div>
                          
                          <Button 
                            onClick={() => handleServeCustomer(nextCustomer)}
                            disabled={isProcessing || workflowState.isServing}
                            className="bg-primary text-primary-foreground hover:bg-primary/90"
                            size="lg"
                          >
                            <UserCheck className="h-4 w-4 mr-2" />
                            Serve Now
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Waiting Customers */}
                <AnimatePresence>
                  {waitingCustomers.map((customer, index) => (
                    <motion.div
                      key={customer.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between p-4 rounded-lg border bg-muted/30 hover:bg-muted/50 transition-all"
                    >
                      <div className="flex items-center gap-4">
                        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-muted text-muted-foreground font-bold">
                          {customer.position}
                        </div>
                        <div>
                          <div className="font-semibold">{customer.customer_name}</div>
                          <div className="text-sm text-muted-foreground flex items-center gap-2">
                            <Phone className="h-3 w-3" />
                            {customer.customer_phone}
                            <span>•</span>
                            <Clock className="h-3 w-3" />
                            Joined {new Date(customer.joined_at).toLocaleTimeString('en-IE', { 
                              hour: '2-digit', 
                              minute: '2-digit' 
                            })}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="font-medium text-sm">
                            ~{customer.estimated_wait} min
                          </div>
                          <div className="text-xs text-muted-foreground">
                            estimated wait
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {getStatusBadge(customer.status)}
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => queueService.cancelCustomer(customer.id)}
                            disabled={isProcessing}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedQueueManager;