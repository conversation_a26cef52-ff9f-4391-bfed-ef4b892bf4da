import { useState, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Plus, Store } from "lucide-react";
import { Business } from "@/lib/types";
import { businessService } from "@/lib/queueService";
import { useToast } from "@/hooks/use-toast";

interface BusinessSelectorProps {
  selectedBusiness: Business | null;
  onBusinessChange: (business: Business | null) => void;
}

const BusinessSelector = ({ selectedBusiness, onBusinessChange }: BusinessSelectorProps) => {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newBusiness, setNewBusiness] = useState({
    name: "",
    type: "salon" as Business['type'],
    phone: "",
    staff_count: 1,
    avg_service_time: 30
  });
  
  const { toast } = useToast();

  useEffect(() => {
    loadBusinesses();
  }, []);

  const loadBusinesses = async () => {
    try {
      const data = await businessService.getAll();
      setBusinesses(data);
      if (data.length > 0 && !selectedBusiness) {
        onBusinessChange(data[0]);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load businesses",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateBusiness = async () => {
    if (!newBusiness.name || !newBusiness.phone) {
      toast({
        title: "Error", 
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    setIsCreating(true);
    try {
    const business = await businessService.create({
      ...newBusiness,
      status: 'open' as const
    });
      setBusinesses(prev => [...prev, business]);
      onBusinessChange(business);
      setIsDialogOpen(false);
      setNewBusiness({
        name: "",
        type: "salon",
        phone: "",
        staff_count: 1,
        avg_service_time: 30
      });
      toast({
        title: "Success",
        description: "Business created successfully"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create business",
        variant: "destructive"
      });
    } finally {
      setIsCreating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-success';
      case 'busy': return 'bg-warning';
      case 'paused': return 'bg-muted';
      case 'closed': return 'bg-destructive';
      default: return 'bg-muted';
    }
  };

  if (isLoading) {
    return <div className="animate-pulse h-10 bg-muted rounded-md"></div>;
  }

  return (
    <div className="flex items-center gap-4">
      <div className="flex-1">
        <Select
          value={selectedBusiness?.id || ""}
          onValueChange={(value) => {
            const business = businesses.find(b => b.id === value);
            onBusinessChange(business || null);
          }}
        >
          <SelectTrigger className="min-w-[200px]">
            <SelectValue placeholder="Select a business">
              {selectedBusiness && (
                <div className="flex items-center gap-2">
                  <Store className="h-4 w-4" />
                  <span>{selectedBusiness.name}</span>
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(selectedBusiness.status)}`}></div>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {businesses.map((business) => (
              <SelectItem key={business.id} value={business.id}>
                <div className="flex items-center gap-2">
                  <Store className="h-4 w-4" />
                  <span>{business.name}</span>
                  <Badge variant="outline" className="ml-auto text-xs">
                    {business.type}
                  </Badge>
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(business.status)}`}></div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Business
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Business</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Business Name *</Label>
              <Input
                id="name"
                value={newBusiness.name}
                onChange={(e) => setNewBusiness(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Bella's Hair Salon"
              />
            </div>
            
            <div>
              <Label htmlFor="type">Business Type</Label>
              <Select
                value={newBusiness.type}
                onValueChange={(value: Business['type']) => 
                  setNewBusiness(prev => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="salon">Hair Salon</SelectItem>
                  <SelectItem value="clinic">Medical Clinic</SelectItem>
                  <SelectItem value="barbershop">Barbershop</SelectItem>
                  <SelectItem value="restaurant">Restaurant</SelectItem>
                  <SelectItem value="dental">Dental Practice</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="phone">Phone Number *</Label>
              <Input
                id="phone"
                value={newBusiness.phone}
                onChange={(e) => setNewBusiness(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+353 87 123 4567"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="staff">Staff Count</Label>
                <Input
                  id="staff"
                  type="number"
                  min="1"
                  value={newBusiness.staff_count}
                  onChange={(e) => setNewBusiness(prev => ({ 
                    ...prev, 
                    staff_count: parseInt(e.target.value) || 1 
                  }))}
                />
              </div>
              <div>
                <Label htmlFor="service-time">Avg Service Time (min)</Label>
                <Input
                  id="service-time"
                  type="number"
                  min="5"
                  value={newBusiness.avg_service_time}
                  onChange={(e) => setNewBusiness(prev => ({ 
                    ...prev, 
                    avg_service_time: parseInt(e.target.value) || 30 
                  }))}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button 
                variant="outline" 
                onClick={() => setIsDialogOpen(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateBusiness} disabled={isCreating}>
                {isCreating ? "Creating..." : "Create Business"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BusinessSelector;