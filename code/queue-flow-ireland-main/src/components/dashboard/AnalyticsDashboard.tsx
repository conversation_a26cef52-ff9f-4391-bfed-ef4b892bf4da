import { useState, useEffect } from "react";
import { Business, BusinessStats } from "@/lib/types";
import { analyticsService } from "@/lib/queueService";
import RealTimeAnalytics from "@/components/analytics/RealTimeAnalytics";

interface AnalyticsDashboardProps {
  business: Business;
}

const AnalyticsDashboard = ({ business }: AnalyticsDashboardProps) => {
  const [stats, setStats] = useState<BusinessStats>({
    todayCustomers: 0,
    avgWaitTime: 0,
    completionRate: 0,
    queueEfficiency: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadStats();
    
    // Update analytics every 30 seconds
    const interval = setInterval(loadStats, 30000);
    return () => clearInterval(interval);
  }, [business.id]);

  const loadStats = async () => {
    try {
      const businessStats = await analyticsService.getBusinessStats(business.id);
      setStats(businessStats);
      
      // Update daily analytics in background
      await analyticsService.updateDailyAnalytics(business.id);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map(i => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-muted rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Real-time Analytics Component */}
      <RealTimeAnalytics business={business} />
    </div>
  );
};

export default AnalyticsDashboard;