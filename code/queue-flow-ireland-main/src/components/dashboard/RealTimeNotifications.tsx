import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { 
  MessageSquare, 
  Mail, 
  Phone, 
  Send, 
  Users,
  Bell
} from "lucide-react";
import { QueueCustomer } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";

interface RealTimeNotificationsProps {
  queue: QueueCustomer[];
  businessName: string;
}

const RealTimeNotifications = ({ queue, businessName }: RealTimeNotificationsProps) => {
  const [notificationSettings, setNotificationSettings] = useState({
    whatsapp: true,
    sms: true,
    email: false,
    autoUpdate: true
  });
  const [customMessage, setCustomMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const sendNotification = async (customers: QueueCustomer[], message: string, type: 'whatsapp' | 'sms' | 'email') => {
    setIsLoading(true);
    try {
      // Simulate sending notifications
      for (const customer of customers) {
        console.log(`Sending ${type} to ${customer.customer_name} (${customer.customer_phone}): ${message}`);
        
        // In real implementation, integrate with WhatsApp Business API, Twilio, etc.
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      toast({
        title: "Notifications Sent",
        description: `${type.toUpperCase()} sent to ${customers.length} customers`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to send ${type} notifications`,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const sendWaitTimeUpdates = async () => {
    const waitingCustomers = queue.filter(c => c.status === 'waiting');
    if (waitingCustomers.length === 0) return;

    const message = `Hi from ${businessName}! Your current wait time is approximately ${waitingCustomers[0]?.estimated_wait || 5} minutes. We'll update you when it's almost your turn. Thank you for your patience!`;
    
    if (notificationSettings.whatsapp) {
      await sendNotification(waitingCustomers, message, 'whatsapp');
    }
    if (notificationSettings.sms) {
      await sendNotification(waitingCustomers, message, 'sms');
    }
  };

  const sendCustomMessage = async () => {
    if (!customMessage.trim()) return;
    
    const allCustomers = queue.filter(c => c.status !== 'completed' && c.status !== 'cancelled');
    await sendNotification(allCustomers, customMessage, 'whatsapp');
    setCustomMessage("");
  };

  const sendNextCustomerAlert = async () => {
    const nextCustomer = queue.find(c => c.status === 'next');
    if (!nextCustomer) return;

    const message = `Hi ${nextCustomer.customer_name}! You're next in line at ${businessName}. Please be ready when we call you. See you soon!`;
    await sendNotification([nextCustomer], message, 'whatsapp');
  };

  // Auto-send updates when queue changes (in real implementation)
  useEffect(() => {
    if (notificationSettings.autoUpdate) {
      // Auto-notify next customer when their status changes
      const nextCustomer = queue.find(c => c.status === 'next');
      if (nextCustomer) {
        // In real app, check if this is a new "next" customer and send notification
      }
    }
  }, [queue, notificationSettings.autoUpdate]);

  const waitingCount = queue.filter(c => c.status === 'waiting').length;
  const nextCustomer = queue.find(c => c.status === 'next');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Real-Time Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Notification Settings */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                <Label>WhatsApp</Label>
              </div>
              <Switch 
                checked={notificationSettings.whatsapp}
                onCheckedChange={(checked) => 
                  setNotificationSettings(prev => ({ ...prev, whatsapp: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <Label>SMS</Label>
              </div>
              <Switch 
                checked={notificationSettings.sms}
                onCheckedChange={(checked) => 
                  setNotificationSettings(prev => ({ ...prev, sms: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <Label>Email</Label>
              </div>
              <Switch 
                checked={notificationSettings.email}
                onCheckedChange={(checked) => 
                  setNotificationSettings(prev => ({ ...prev, email: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <Label>Auto Updates</Label>
              <Switch 
                checked={notificationSettings.autoUpdate}
                onCheckedChange={(checked) => 
                  setNotificationSettings(prev => ({ ...prev, autoUpdate: checked }))
                }
              />
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button 
              onClick={sendWaitTimeUpdates}
              disabled={isLoading || waitingCount === 0}
              className="w-full"
            >
              <Users className="h-4 w-4 mr-2" />
              Update Wait Times ({waitingCount})
            </Button>
            
            <Button 
              onClick={sendNextCustomerAlert}
              disabled={isLoading || !nextCustomer}
              variant="outline"
              className="w-full"
            >
              <Bell className="h-4 w-4 mr-2" />
              Alert Next Customer
            </Button>
            
            <Badge variant="outline" className="flex items-center justify-center py-2">
              {queue.length} customers in queue
            </Badge>
          </div>

          {/* Custom Message */}
          <div className="space-y-2">
            <Label>Send Custom Message</Label>
            <div className="flex gap-2">
              <Textarea
                placeholder="e.g., We're running 5 minutes behind schedule..."
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                className="min-h-[60px] flex-1"
              />
              <Button 
                onClick={sendCustomMessage}
                disabled={isLoading || !customMessage.trim()}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Current Status */}
          <div className="p-3 bg-muted/30 rounded-lg">
            <div className="text-sm text-muted-foreground mb-1">Current Queue Status:</div>
            <div className="flex items-center gap-4 text-sm">
              <span><strong>{waitingCount}</strong> waiting</span>
              <span><strong>{queue.filter(c => c.status === 'serving').length}</strong> being served</span>
              <span><strong>{queue.filter(c => c.status === 'completed').length}</strong> completed</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RealTimeNotifications;