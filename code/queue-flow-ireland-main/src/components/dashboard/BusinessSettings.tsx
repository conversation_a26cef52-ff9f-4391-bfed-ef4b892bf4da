import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Store, 
  Users, 
  Bell, 
  Clock, 
  Euro, 
  Shield, 
  Palette,
  MessageSquare,
  Mail,
  Phone
} from "lucide-react";
import { Business } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface BusinessSettingsProps {
  business: Business;
}

const BusinessSettings = ({ business }: BusinessSettingsProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    name: business.name,
    type: business.type,
    phone: business.phone,
    staff_count: business.staff_count,
    avg_service_time: business.avg_service_time,
    status: business.status
  });

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('businesses')
        .update({
          name: formData.name,
          type: formData.type,
          phone: formData.phone,
          staff_count: formData.staff_count,
          avg_service_time: formData.avg_service_time,
          status: formData.status,
          updated_at: new Date().toISOString()
        })
        .eq('id', business.id);

      if (error) throw error;

      toast({
        title: "Settings Saved",
        description: "Your business settings have been updated successfully."
      });
    } catch (error) {
      console.error('Save error:', error);
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold">Business Settings</h3>
          <p className="text-muted-foreground">Manage your business configuration and preferences</p>
        </div>
        <Button onClick={handleSave} disabled={isLoading} className="btn-primary">
          {isLoading ? "Saving..." : "Save Changes"}
        </Button>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">
            <Store className="h-4 w-4 mr-2" />
            General
          </TabsTrigger>
          <TabsTrigger value="hours">
            <Clock className="h-4 w-4 mr-2" />
            Hours
          </TabsTrigger>
          <TabsTrigger value="staff">
            <Users className="h-4 w-4 mr-2" />
            Staff
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="pricing">
            <Euro className="h-4 w-4 mr-2" />
            Pricing
          </TabsTrigger>
          <TabsTrigger value="security">
            <Shield className="h-4 w-4 mr-2" />
            Security
          </TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Business Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="business-name">Business Name</Label>
                  <Input 
                    id="business-name" 
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="business-type">Business Type</Label>
                  <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value as any }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="salon">Hair Salon</SelectItem>
                      <SelectItem value="barbershop">Barbershop</SelectItem>
                      <SelectItem value="clinic">Medical Clinic</SelectItem>
                      <SelectItem value="dental">Dental Practice</SelectItem>
                      <SelectItem value="restaurant">Restaurant</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="business-description">Description</Label>
                <Textarea 
                  id="business-description" 
                  placeholder="Describe your business and services..."
                  className="min-h-[100px]"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="business-phone">Phone Number</Label>
                  <Input 
                    id="business-phone" 
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="business-email">Email Address</Label>
                  <Input id="business-email" type="email" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Queue Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="max-capacity">Maximum Queue Capacity</Label>
                  <Input id="max-capacity" type="number" defaultValue="50" />
                </div>
                <div>
                  <Label htmlFor="avg-service">Average Service Time (minutes)</Label>
                  <Input 
                    id="avg-service" 
                    type="number" 
                    value={formData.avg_service_time}
                    onChange={(e) => setFormData(prev => ({ ...prev, avg_service_time: parseInt(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="staff-count">Number of Staff</Label>
                  <Input 
                    id="staff-count" 
                    type="number" 
                    value={formData.staff_count}
                    onChange={(e) => setFormData(prev => ({ ...prev, staff_count: parseInt(e.target.value) }))}
                  />
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>Allow Walk-ins</Label>
                  <p className="text-sm text-muted-foreground">Enable staff to add customers directly</p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Operating Hours */}
        <TabsContent value="hours" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Operating Hours</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                <div key={day} className="flex items-center justify-between p-4 border border-border rounded-lg">
                  <div className="flex items-center gap-4">
                    <Switch defaultChecked={day !== 'Sunday'} />
                    <span className="font-medium w-20">{day}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Input type="time" defaultValue="09:00" className="w-32" />
                    <span>to</span>
                    <Input type="time" defaultValue="17:00" className="w-32" />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Break Times</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Lunch Break</Label>
                  <p className="text-sm text-muted-foreground">Automatically pause queue during break</p>
                </div>
                <Switch />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Start Time</Label>
                  <Input type="time" defaultValue="12:30" />
                </div>
                <div>
                  <Label>End Time</Label>
                  <Input type="time" defaultValue="13:30" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Staff Management */}
        <TabsContent value="staff" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Staff Members
                <Button variant="outline" size="sm">
                  <Users className="h-4 w-4 mr-2" />
                  Add Staff
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { name: "John Smith", role: "Owner", email: "<EMAIL>", active: true },
                { name: "Sarah Johnson", role: "Staff", email: "<EMAIL>", active: true },
                { name: "Mike O'Brien", role: "Staff", email: "<EMAIL>", active: false }
              ].map((staff, index) => (
                <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <Users className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">{staff.name}</div>
                      <div className="text-sm text-muted-foreground">{staff.email}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <Badge variant={staff.active ? "default" : "secondary"}>
                      {staff.role}
                    </Badge>
                    <Switch defaultChecked={staff.active} />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                WhatsApp Notifications
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Enable WhatsApp Notifications</Label>
                  <p className="text-sm text-muted-foreground">Send queue updates via WhatsApp</p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>WhatsApp Business Number</Label>
                  <Input defaultValue="+353 87 123 4567" />
                </div>
                <div>
                  <Label>Message Language</Label>
                  <Select defaultValue="en">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="ga">Irish (Gaeilge)</SelectItem>
                      <SelectItem value="pl">Polish</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Notifications
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { label: "Daily Summary Reports", description: "Receive end-of-day analytics", enabled: true },
                { label: "Queue Alerts", description: "Notify when queue gets too long", enabled: true },
                { label: "Staff Notifications", description: "Send updates to staff members", enabled: false }
              ].map((notification, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <Label>{notification.label}</Label>
                    <p className="text-sm text-muted-foreground">{notification.description}</p>
                  </div>
                  <Switch defaultChecked={notification.enabled} />
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pricing Settings */}
        <TabsContent value="pricing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Service Pricing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full">
                <Euro className="h-4 w-4 mr-2" />
                Add Service Category
              </Button>
              {[
                { service: "Haircut", price: "25", duration: "30" },
                { service: "Hair Wash & Blow Dry", price: "15", duration: "20" },
                { service: "Color Treatment", price: "75", duration: "90" }
              ].map((item, index) => (
                <div key={index} className="grid grid-cols-3 gap-4 p-4 border border-border rounded-lg">
                  <div>
                    <Label>Service Name</Label>
                    <Input defaultValue={item.service} />
                  </div>
                  <div>
                    <Label>Price (€)</Label>
                    <Input type="number" defaultValue={item.price} />
                  </div>
                  <div>
                    <Label>Duration (minutes)</Label>
                    <Input type="number" defaultValue={item.duration} />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Access Control</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Require Staff Login</Label>
                  <p className="text-sm text-muted-foreground">Staff must authenticate to access dashboard</p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>GDPR Compliance Mode</Label>
                  <p className="text-sm text-muted-foreground">Enhanced privacy and data protection</p>
                </div>
                <Switch defaultChecked />
              </div>
              <div>
                <Label>Data Retention Period</Label>
                <Select defaultValue="90">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 days</SelectItem>
                    <SelectItem value="90">90 days</SelectItem>
                    <SelectItem value="180">180 days</SelectItem>
                    <SelectItem value="365">1 year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default BusinessSettings;