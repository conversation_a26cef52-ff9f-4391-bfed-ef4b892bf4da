import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, ArrowRight, Star } from "lucide-react";

const Pricing = () => {
  const plans = [
    {
      name: "Free",
      price: "€0",
      period: "forever",
      description: "Perfect for trying QueueFlow with your business",
      customers: "Up to 50 customers/month",
      badge: null,
      features: [
        "Basic queue management",
        "WhatsApp & SMS integration", 
        "7-day data retention",
        "Basic analytics",
        "Email support"
      ],
      cta: "Start Free",
      variant: "outline" as const,
      popular: false
    },
    {
      name: "Starter",
      price: "€15",
      period: "month",
      description: "Ideal for small salons, clinics, and cafes",
      customers: "Up to 200 customers/month",
      badge: "Most Popular",
      features: [
        "Everything in Free",
        "AI wait time predictions",
        "30-day data retention",
        "Advanced analytics dashboard",
        "Phone support",
        "Custom business branding"
      ],
      cta: "Start Free Trial",
      variant: "default" as const,
      popular: true
    },
    {
      name: "Growth",
      price: "€35", 
      period: "month",
      description: "For growing businesses with multiple staff",
      customers: "Up to 500 customers/month",
      badge: "Business",
      features: [
        "Everything in Starter",
        "Multi-staff management",
        "Appointment integration",
        "Priority support",
        "Advanced AI features",
        "Export capabilities",
        "API access"
      ],
      cta: "Start Free Trial",
      variant: "secondary" as const,
      popular: false
    },
    {
      name: "Scale",
      price: "€75",
      period: "month", 
      description: "For businesses with multiple locations",
      customers: "Unlimited customers",
      badge: "Enterprise",
      features: [
        "Everything in Growth",
        "Multi-location support",
        "Dedicated account manager",
        "Custom integrations",
        "99.9% SLA guarantee",
        "Advanced reporting",
        "White-label options"
      ],
      cta: "Contact Sales",
      variant: "outline" as const,
      popular: false
    }
  ];

  return (
    <section className="section-padding bg-muted/30">
      <div className="container mx-auto container-padding">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <Badge variant="secondary" className="mb-4">
            Pricing
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            Simple, transparent{" "}
            <span className="text-gradient">pricing</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Start free and scale as you grow. All plans include our core features 
            with no hidden costs or setup fees.
          </p>
          
          {/* Value Proposition */}
          <div className="bg-primary/10 border border-primary/20 rounded-lg p-4 max-w-md mx-auto">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <Star className="h-5 w-5 text-primary fill-primary" />
              <span className="font-semibold text-primary">ROI Guarantee</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Average 16x return on investment within first 3 months
            </p>
          </div>
        </div>

        {/* Pricing Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {plans.map((plan, index) => (
            <Card 
              key={index}
              className={`card-hover relative ${
                plan.popular 
                  ? 'border-primary/50 shadow-glow scale-105' 
                  : 'border-border'
              }`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {plan.badge && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge 
                    className={
                      plan.popular 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-secondary text-secondary-foreground'
                    }
                  >
                    {plan.badge}
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-2xl font-bold mb-2">
                  {plan.name}
                </CardTitle>
                <div className="mb-4">
                  <span className="text-4xl font-bold">{plan.price}</span>
                  <span className="text-muted-foreground">/{plan.period}</span>
                </div>
                <p className="text-muted-foreground text-sm mb-4">
                  {plan.description}
                </p>
                <div className="bg-muted/50 rounded-lg p-3">
                  <p className="text-sm font-medium text-center">
                    {plan.customers}
                  </p>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start space-x-3">
                      <Check className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-muted-foreground">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  className={`w-full ${
                    plan.popular 
                      ? 'btn-primary' 
                      : plan.variant === 'secondary'
                      ? 'btn-secondary'
                      : ''
                  }`}
                  variant={plan.variant}
                  size="lg"
                >
                  {plan.cta}
                  {plan.cta !== 'Contact Sales' && (
                    <ArrowRight className="ml-2 h-4 w-4" />
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-16 text-center">
          <div className="bg-background rounded-lg p-6 max-w-4xl mx-auto shadow-md">
            <h3 className="text-lg font-semibold mb-4">
              All plans include:
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-primary" />
                <span>GDPR compliance</span>
              </div>
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-primary" />
                <span>Irish data residency</span>
              </div>
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-primary" />
                <span>Multi-language support</span>
              </div>
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-primary" />
                <span>Cancel anytime</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Pricing;