import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Calendar, QrCode, Settings, BarChart3, Users } from "lucide-react";
import { Business } from "@/lib/types";
import BusinessSelector from "@/components/dashboard/BusinessSelector";
import QueueManager from "@/components/dashboard/QueueManager";
import AnalyticsDashboard from "@/components/dashboard/AnalyticsDashboard";
import QRCodeGenerator from "@/components/customer/QRCodeGenerator";

const BusinessDashboard = () => {
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);

  return (
    <section className="section-padding bg-background">
      <div className="container mx-auto container-padding">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12 animate-fade-in-up">
            <Badge variant="secondary" className="mb-4">
              Business Dashboard
            </Badge>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Manage your queue with{" "}
              <span className="text-gradient">real-time insights</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Simple, powerful dashboard that works on any device. Monitor your queue, 
              serve customers, and track performance - all in one place.
            </p>
          </div>

          {/* Business Selector */}
          <div className="mb-8">
            <BusinessSelector 
              selectedBusiness={selectedBusiness}
              onBusinessChange={setSelectedBusiness}
            />
          </div>

          {/* Dashboard Content */}
          {selectedBusiness ? (
            <div className="bg-gradient-subtle rounded-2xl p-6 md:p-8 shadow-xl">
              {/* Dashboard Header */}
              <div className="flex items-center justify-between mb-8">
                <div>
                  <h3 className="text-2xl font-bold mb-2">{selectedBusiness.name}</h3>
                  <p className="text-muted-foreground flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {new Date().toLocaleDateString('en-IE', { 
                      weekday: 'long', 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}
                    <span>•</span>
                    {new Date().toLocaleTimeString('en-IE', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>
                <div className="flex items-center gap-4">
                  <Badge variant="outline" className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      selectedBusiness.status === 'open' ? 'bg-success' :
                      selectedBusiness.status === 'busy' ? 'bg-warning' :
                      selectedBusiness.status === 'paused' ? 'bg-muted' :
                      'bg-destructive'
                    } animate-pulse`}></div>
                    <span className="capitalize">{selectedBusiness.status}</span>
                  </Badge>
                </div>
              </div>

              {/* Dashboard Tabs */}
              <Tabs defaultValue="queue" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4 lg:w-[400px]">
                  <TabsTrigger value="queue" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Queue
                  </TabsTrigger>
                  <TabsTrigger value="analytics" className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Analytics
                  </TabsTrigger>
                  <TabsTrigger value="qrcode" className="flex items-center gap-2">
                    <QrCode className="h-4 w-4" />
                    QR Code
                  </TabsTrigger>
                  <TabsTrigger value="settings" className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Settings
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="queue" className="space-y-6">
                  <QueueManager business={selectedBusiness} />
                </TabsContent>

                <TabsContent value="analytics" className="space-y-6">
                  <AnalyticsDashboard business={selectedBusiness} />
                </TabsContent>

                <TabsContent value="qrcode" className="space-y-6">
                  <QRCodeGenerator business={selectedBusiness} />
                </TabsContent>

                <TabsContent value="settings" className="space-y-6">
                  <div className="text-center py-8 text-muted-foreground">
                    <Settings className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p>Settings panel coming soon</p>
                    <p className="text-sm">Manage business settings, staff, and preferences</p>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <Users className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-xl font-semibold mb-2">Select or Create a Business</h3>
              <p>Choose an existing business or create a new one to start managing your queue.</p>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default BusinessDashboard;