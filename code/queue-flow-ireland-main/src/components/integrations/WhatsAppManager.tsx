import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MessageCircle, 
  Send, 
  Settings, 
  Users, 
  Clock,
  CheckCircle,
  AlertCircle,
  Globe,
  Phone
} from 'lucide-react';
import { Business, QueueCustomer } from '@/lib/types';
import { toast } from 'sonner';

interface WhatsAppManagerProps {
  business: Business;
  queue: QueueCustomer[];
}

interface WhatsAppMessage {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone: string;
  message: string;
  type: 'incoming' | 'outgoing' | 'automated';
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read' | 'failed';
}

interface WhatsAppSettings {
  enabled: boolean;
  businessPhoneNumber: string;
  welcomeMessage: string;
  queueJoinMessage: string;
  positionUpdateMessage: string;
  servingMessage: string;
  completedMessage: string;
  languages: string[];
  autoResponses: boolean;
}

export const WhatsAppManager: React.FC<WhatsAppManagerProps> = ({
  business,
  queue
}) => {
  const [messages, setMessages] = useState<WhatsAppMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [settings, setSettings] = useState<WhatsAppSettings>({
    enabled: false,
    businessPhoneNumber: '+353',
    welcomeMessage: 'Welcome to {{businessName}}! You can join our queue by replying with "JOIN".',
    queueJoinMessage: 'Great! You\'ve been added to the queue at position {{position}}. Estimated wait time: {{waitTime}} minutes.',
    positionUpdateMessage: 'Queue update: You\'re now at position {{position}}. Estimated wait time: {{waitTime}} minutes.',
    servingMessage: 'It\'s your turn! Please come to the service area.',
    completedMessage: 'Thank you for visiting {{businessName}}! We hope to see you again soon.',
    languages: ['en', 'ga', 'pl', 'lt'],
    autoResponses: true
  });

  const [stats, setStats] = useState({
    totalMessages: 0,
    activeChats: 0,
    responseRate: 0,
    avgResponseTime: 0
  });

  useEffect(() => {
    // Simulate loading existing messages
    const sampleMessages: WhatsAppMessage[] = [
      {
        id: '1',
        customerId: 'cust1',
        customerName: 'John Doe',
        customerPhone: '+353851234567',
        message: 'Hi, can I join the queue?',
        type: 'incoming',
        timestamp: new Date(Date.now() - 300000),
        status: 'read'
      },
      {
        id: '2',
        customerId: 'cust1',
        customerName: 'John Doe',
        customerPhone: '+353851234567',
        message: 'You\'ve been added to the queue at position 3. Estimated wait time: 15 minutes.',
        type: 'automated',
        timestamp: new Date(Date.now() - 290000),
        status: 'delivered'
      }
    ];
    setMessages(sampleMessages);

    // Update stats
    setStats({
      totalMessages: sampleMessages.length,
      activeChats: 1,
      responseRate: 98,
      avgResponseTime: 2.5
    });
  }, []);

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedCustomer) return;

    const message: WhatsAppMessage = {
      id: Date.now().toString(),
      customerId: selectedCustomer,
      customerName: 'Selected Customer',
      customerPhone: '+353851234567',
      message: newMessage,
      type: 'outgoing',
      timestamp: new Date(),
      status: 'sent'
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
    toast.success('Message sent successfully!');
  };

  const sendBulkUpdate = async (messageType: string) => {
    const activeCustomers = queue.filter(c => ['waiting', 'next'].includes(c.status));
    
    activeCustomers.forEach(customer => {
      let messageTemplate = '';
      switch (messageType) {
        case 'position':
          messageTemplate = settings.positionUpdateMessage;
          break;
        case 'delay':
          messageTemplate = 'We apologize for the delay. Your current position is {{position}} with an estimated wait of {{waitTime}} minutes.';
          break;
        default:
          return;
      }

      const message = messageTemplate
        .replace('{{businessName}}', business.name)
        .replace('{{position}}', customer.position.toString())
        .replace('{{waitTime}}', (customer.estimated_wait || 0).toString());

      // Simulate sending message
      console.log(`Sending to ${customer.customer_phone}: ${message}`);
    });

    toast.success(`Bulk update sent to ${activeCustomers.length} customers!`);
  };

  const toggleWhatsApp = async (enabled: boolean) => {
    setSettings(prev => ({ ...prev, enabled }));
    
    if (enabled) {
      toast.success('WhatsApp integration enabled! Customers can now join via WhatsApp.');
    } else {
      toast.info('WhatsApp integration disabled.');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <MessageCircle className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Messages</p>
                  <p className="text-2xl font-bold">{stats.totalMessages}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-accent" />
                <div>
                  <p className="text-sm text-muted-foreground">Active Chats</p>
                  <p className="text-2xl font-bold">{stats.activeChats}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-success" />
                <div>
                  <p className="text-sm text-muted-foreground">Response Rate</p>
                  <p className="text-2xl font-bold">{stats.responseRate}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-warning" />
                <div>
                  <p className="text-sm text-muted-foreground">Avg Response</p>
                  <p className="text-2xl font-bold">{stats.avgResponseTime}m</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <Tabs defaultValue="messages" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="messages">Messages</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Actions</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="messages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MessageCircle className="h-5 w-5" />
                <span>WhatsApp Messages</span>
                <Badge variant={settings.enabled ? "default" : "secondary"}>
                  {settings.enabled ? "Active" : "Inactive"}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Customer List */}
                <div className="lg:w-1/3">
                  <h4 className="font-medium mb-3">Recent Chats</h4>
                  <div className="space-y-2">
                    {messages
                      .reduce((acc, msg) => {
                        if (!acc.find(m => m.customerId === msg.customerId)) {
                          acc.push(msg);
                        }
                        return acc;
                      }, [] as WhatsAppMessage[])
                      .map((msg) => (
                        <motion.div
                          key={msg.customerId}
                          whileHover={{ scale: 1.01 }}
                          className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                            selectedCustomer === msg.customerId
                              ? 'bg-primary/10 border-primary'
                              : 'hover:bg-muted/50'
                          }`}
                          onClick={() => setSelectedCustomer(msg.customerId)}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-sm">{msg.customerName}</p>
                              <p className="text-xs text-muted-foreground">{msg.customerPhone}</p>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {msg.status}
                            </Badge>
                          </div>
                        </motion.div>
                      ))}
                  </div>
                </div>

                {/* Chat Area */}
                <div className="lg:w-2/3">
                  {selectedCustomer ? (
                    <div className="border rounded-lg h-96 flex flex-col">
                      {/* Messages */}
                      <div className="flex-1 p-4 overflow-y-auto space-y-3">
                        <AnimatePresence>
                          {messages
                            .filter(msg => msg.customerId === selectedCustomer)
                            .map((msg) => (
                              <motion.div
                                key={msg.id}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                className={`flex ${
                                  msg.type === 'incoming' ? 'justify-start' : 'justify-end'
                                }`}
                              >
                                <div
                                  className={`max-w-xs p-3 rounded-lg ${
                                    msg.type === 'incoming'
                                      ? 'bg-muted'
                                      : msg.type === 'automated'
                                      ? 'bg-primary/20'
                                      : 'bg-primary text-primary-foreground'
                                  }`}
                                >
                                  <p className="text-sm">{msg.message}</p>
                                  <p className="text-xs opacity-70 mt-1">
                                    {msg.timestamp.toLocaleTimeString()}
                                  </p>
                                </div>
                              </motion.div>
                            ))}
                        </AnimatePresence>
                      </div>

                      {/* Input */}
                      <div className="p-4 border-t">
                        <div className="flex space-x-2">
                          <Input
                            placeholder="Type a message..."
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                          />
                          <Button onClick={sendMessage} disabled={!newMessage.trim()}>
                            <Send className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="border rounded-lg h-96 flex items-center justify-center text-muted-foreground">
                      Select a customer to view messages
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Message Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  onClick={() => sendBulkUpdate('position')}
                  className="h-16 flex flex-col items-center justify-center"
                >
                  <Users className="h-5 w-5 mb-1" />
                  Send Position Updates
                  <span className="text-xs opacity-70">
                    to {queue.filter(c => ['waiting', 'next'].includes(c.status)).length} customers
                  </span>
                </Button>

                <Button
                  variant="outline"
                  onClick={() => sendBulkUpdate('delay')}
                  className="h-16 flex flex-col items-center justify-center"
                >
                  <AlertCircle className="h-5 w-5 mb-1" />
                  Send Delay Notice
                  <span className="text-xs opacity-70">
                    Notify about unexpected delays
                  </span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>WhatsApp Integration Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Enable/Disable */}
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base">Enable WhatsApp Integration</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow customers to join queue via WhatsApp
                  </p>
                </div>
                <Switch
                  checked={settings.enabled}
                  onCheckedChange={toggleWhatsApp}
                />
              </div>

              {/* Business Phone */}
              <div className="space-y-2">
                <Label htmlFor="phone">Business WhatsApp Number</Label>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4" />
                  <Input
                    id="phone"
                    placeholder="+353 85 123 4567"
                    value={settings.businessPhoneNumber}
                    onChange={(e) => setSettings(prev => ({ 
                      ...prev, 
                      businessPhoneNumber: e.target.value 
                    }))}
                  />
                </div>
              </div>

              {/* Message Templates */}
              <div className="space-y-4">
                <h4 className="font-medium">Message Templates</h4>
                
                <div className="space-y-2">
                  <Label>Welcome Message</Label>
                  <Textarea
                    placeholder="Welcome message..."
                    value={settings.welcomeMessage}
                    onChange={(e) => setSettings(prev => ({ 
                      ...prev, 
                      welcomeMessage: e.target.value 
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Queue Join Confirmation</Label>
                  <Textarea
                    placeholder="Queue join message..."
                    value={settings.queueJoinMessage}
                    onChange={(e) => setSettings(prev => ({ 
                      ...prev, 
                      queueJoinMessage: e.target.value 
                    }))}
                  />
                </div>
              </div>

              {/* Auto Responses */}
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base">Auto Responses</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically respond to common messages
                  </p>
                </div>
                <Switch
                  checked={settings.autoResponses}
                  onCheckedChange={(checked) => setSettings(prev => ({ 
                    ...prev, 
                    autoResponses: checked 
                  }))}
                />
              </div>

              {/* Languages */}
              <div className="space-y-2">
                <Label>Supported Languages</Label>
                <div className="flex items-center space-x-2">
                  <Globe className="h-4 w-4" />
                  <div className="flex flex-wrap gap-2">
                    {['English', 'Irish (Gaeilge)', 'Polish', 'Lithuanian'].map((lang, index) => (
                      <Badge key={lang} variant="outline">
                        {lang}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};