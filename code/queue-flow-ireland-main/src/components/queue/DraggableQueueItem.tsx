import React from 'react';
import { motion, Reorder, AnimatePresence } from 'framer-motion';
import { useDrag, useDrop } from 'react-dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  GripVertical, 
  Phone, 
  Clock, 
  User, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  PlayCircle
} from 'lucide-react';
import { QueueCustomer } from '@/lib/types';
import { cn } from '@/lib/utils';

interface DraggableQueueItemProps {
  customer: QueueCustomer;
  index: number;
  onServe: (customerId: string) => void;
  onComplete: (customerId: string) => void;
  onCancel: (customerId: string) => void;
  onReorder: (customerId: string, newPosition: number) => void;
  isProcessing?: boolean;
}

const ItemType = 'QUEUE_ITEM';

export const DraggableQueueItem: React.FC<DraggableQueueItemProps> = ({
  customer,
  index,
  onServe,
  onComplete,
  onCancel,
  onReorder,
  isProcessing = false,
}) => {
  const [{ isDragging }, drag, preview] = useDrag({
    type: ItemType,
    item: { id: customer.id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [{ isOver }, drop] = useDrop({
    accept: ItemType,
    hover: (draggedItem: { id: string; index: number }) => {
      if (draggedItem.index !== index) {
        onReorder(draggedItem.id, index);
        draggedItem.index = index;
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  const getStatusIcon = (status: QueueCustomer['status']) => {
    switch (status) {
      case 'waiting':
        return <Clock className="h-4 w-4" />;
      case 'next':
        return <AlertCircle className="h-4 w-4" />;
      case 'serving':
        return <PlayCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: QueueCustomer['status']) => {
    switch (status) {
      case 'waiting':
        return 'status-waiting';
      case 'next':
        return 'status-next pulse-primary';
      case 'serving':
        return 'status-serving';
      case 'completed':
        return 'status-completed';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return 'status-waiting';
    }
  };

  const formatWaitTime = (minutes: number | null) => {
    if (!minutes) return 'N/A';
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div ref={(node) => drag(drop(node))}>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ 
          opacity: isDragging ? 0.5 : 1, 
          scale: isDragging ? 1.05 : 1,
          y: isOver ? -2 : 0
        }}
        exit={{ opacity: 0, scale: 0.95 }}
        whileHover={{ scale: 1.02, y: -2 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        className={cn(
          "mb-3 transition-all duration-200",
          isDragging && "drag-preview",
          isOver && "ring-2 ring-primary/50"
        )}
      >
        <Card className={cn(
          "relative overflow-hidden border-l-4",
          getStatusColor(customer.status),
          customer.status === 'next' && "animate-pulse-glow"
        )}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 flex-1">
                {/* Drag Handle */}
                <div className="drag-handle opacity-50 hover:opacity-100">
                  <GripVertical className="h-4 w-4" />
                </div>

                {/* Avatar */}
                <Avatar className="h-10 w-10">
                  <AvatarFallback className="bg-primary/10 text-primary">
                    {getInitials(customer.customer_name)}
                  </AvatarFallback>
                </Avatar>

                {/* Customer Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium truncate">
                      {customer.customer_name}
                    </h4>
                    <Badge 
                      variant="secondary" 
                      className={cn("text-xs", getStatusColor(customer.status))}
                    >
                      {getStatusIcon(customer.status)}
                      <span className="ml-1 capitalize">{customer.status}</span>
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-1 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Phone className="h-3 w-3" />
                      <span>{customer.customer_phone}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span>Wait: {formatWaitTime(customer.estimated_wait)}</span>
                    </div>
                    <span className="text-xs">
                      #{customer.position}
                    </span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                <AnimatePresence>
                  {customer.status === 'waiting' && customer.position === 1 && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                    >
                      <Button
                        size="sm"
                        onClick={() => onServe(customer.id)}
                        disabled={isProcessing}
                        className="bg-primary hover:bg-primary-hover"
                      >
                        <PlayCircle className="h-3 w-3 mr-1" />
                        Serve
                      </Button>
                    </motion.div>
                  )}

                  {customer.status === 'serving' && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                    >
                      <Button
                        size="sm"
                        onClick={() => onComplete(customer.id)}
                        disabled={isProcessing}
                        className="bg-success hover:bg-success/90"
                      >
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Complete
                      </Button>
                    </motion.div>
                  )}

                  {['waiting', 'next'].includes(customer.status) && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                    >
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onCancel(customer.id)}
                        disabled={isProcessing}
                        className="border-destructive/20 hover:bg-destructive/10"
                      >
                        <XCircle className="h-3 w-3 mr-1" />
                        Cancel
                      </Button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Progress Bar for Serving Status */}
            {customer.status === 'serving' && (
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                className="mt-3 h-1 bg-accent/20 rounded-full overflow-hidden"
              >
                <motion.div
                  className="h-full bg-accent rounded-full"
                  animate={{ width: ["0%", "100%"] }}
                  transition={{ duration: 30, ease: "linear" }}
                />
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};