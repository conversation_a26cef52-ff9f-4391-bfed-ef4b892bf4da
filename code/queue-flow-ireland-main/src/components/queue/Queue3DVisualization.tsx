import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Text, Box, Sphere } from '@react-three/drei';
import { motion } from 'framer-motion';
import { QueueCustomer } from '@/lib/types';
import * as THREE from 'three';

interface Queue3DVisualizationProps {
  queue: QueueCustomer[];
  businessName: string;
}

const QueueItem: React.FC<{ 
  customer: QueueCustomer; 
  position: [number, number, number];
  index: number;
}> = ({ customer, position, index }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime + index) * 0.1;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2 + index) * 0.1;
    }
  });

  const getColor = (status: QueueCustomer['status']) => {
    switch (status) {
      case 'waiting': return '#f59e0b'; // warning
      case 'next': return '#10b981'; // primary
      case 'serving': return '#f97316'; // accent  
      case 'completed': return '#059669'; // success
      case 'cancelled': return '#dc2626'; // destructive
      default: return '#6b7280';
    }
  };

  const getScale = (status: QueueCustomer['status']) => {
    switch (status) {
      case 'next': return 1.2;
      case 'serving': return 1.1;
      default: return 1;
    }
  };

  return (
    <group position={position}>
      <Box
        ref={meshRef}
        args={[0.8, 0.8, 0.8]}
        scale={getScale(customer.status)}
      >
        <meshStandardMaterial 
          color={getColor(customer.status)} 
          transparent
          opacity={customer.status === 'cancelled' ? 0.3 : 0.8}
        />
      </Box>
      
      <Text
        position={[0, 1.2, 0]}
        fontSize={0.2}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        {customer.customer_name.split(' ')[0]}
      </Text>
      
      <Text
        position={[0, -1.2, 0]}
        fontSize={0.15}
        color="#888"
        anchorX="center"
        anchorY="middle"
      >
        #{customer.position}
      </Text>
    </group>
  );
};

const Scene: React.FC<{ queue: QueueCustomer[]; businessName: string }> = ({ 
  queue, 
  businessName 
}) => {
  const positions = useMemo(() => {
    const cols = Math.ceil(Math.sqrt(queue.length));
    const rows = Math.ceil(queue.length / cols);
    
    return queue.map((_, index) => {
      const col = index % cols;
      const row = Math.floor(index / cols);
      const x = (col - (cols - 1) / 2) * 2;
      const z = (row - (rows - 1) / 2) * 2;
      return [x, 0, z] as [number, number, number];
    });
  }, [queue.length]);

  return (
    <>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} />
      <pointLight position={[-10, -10, -10]} color="#0ea5e9" intensity={0.3} />
      
      {/* Business Name */}
      <Text
        position={[0, 4, 0]}
        fontSize={0.5}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        {businessName} Queue
      </Text>
      
      {/* Queue Items */}
      {queue.map((customer, index) => (
        <QueueItem
          key={customer.id}
          customer={customer}
          position={positions[index]}
          index={index}
        />
      ))}
      
      {/* Floor */}
      <Box position={[0, -2, 0]} args={[20, 0.1, 20]}>
        <meshStandardMaterial color="#1f2937" transparent opacity={0.3} />
      </Box>
      
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={5}
        maxDistance={20}
      />
    </>
  );
};

export const Queue3DVisualization: React.FC<Queue3DVisualizationProps> = ({ 
  queue, 
  businessName 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="w-full h-96 bg-gradient-to-br from-slate-900 to-slate-700 rounded-lg overflow-hidden"
    >
      <Canvas
        camera={{ position: [5, 5, 5], fov: 60 }}
        style={{ background: 'linear-gradient(to bottom, #0f172a, #1e293b)' }}
      >
        <Scene queue={queue} businessName={businessName} />
      </Canvas>
      
      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm rounded-lg p-3">
        <h4 className="text-white text-sm font-medium mb-2">Status Legend</h4>
        <div className="flex flex-wrap gap-2 text-xs">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-warning rounded"></div>
            <span className="text-white">Waiting</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-primary rounded"></div>
            <span className="text-white">Next</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-accent rounded"></div>
            <span className="text-white">Serving</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-success rounded"></div>
            <span className="text-white">Completed</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};