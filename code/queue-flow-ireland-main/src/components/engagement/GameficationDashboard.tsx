import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Trophy, 
  Target, 
  Zap, 
  Clock, 
  Star,
  Award,
  TrendingUp,
  CheckCircle2,
  Users,
  Timer
} from "lucide-react";
import { motion } from "framer-motion";
import { Business, BusinessStats } from "@/lib/types";

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  points: number;
}

interface DailyGoal {
  id: string;
  title: string;
  description: string;
  target: number;
  current: number;
  points: number;
  icon: React.ElementType;
  completed: boolean;
}

interface GameficationDashboardProps {
  business: Business;
  stats: BusinessStats;
}

const GameficationDashboard = ({ business, stats }: GameficationDashboardProps) => {
  const [level, setLevel] = useState(1);
  const [totalPoints, setTotalPoints] = useState(0);
  const [experiencePoints, setExperiencePoints] = useState(0);
  const [streakDays, setStreakDays] = useState(0);

  // Calculate gamification metrics
  useEffect(() => {
    // Calculate level based on total customers served and efficiency
    const calculatedLevel = Math.floor((stats.todayCustomers * stats.queueEfficiency) / 100) + 1;
    setLevel(Math.max(1, calculatedLevel));
    
    // Calculate points based on performance
    const efficiencyPoints = stats.queueEfficiency * 10;
    const customerPoints = stats.todayCustomers * 5;
    const completionPoints = stats.completionRate * 2;
    const speedBonus = stats.avgWaitTime < 10 ? 100 : stats.avgWaitTime < 15 ? 50 : 0;
    
    const points = efficiencyPoints + customerPoints + completionPoints + speedBonus;
    setTotalPoints(Math.round(points));
    
    // Experience points for next level
    const currentLevelExp = level * 1000;
    const nextLevelExp = (level + 1) * 1000;
    setExperiencePoints(Math.min(points % 1000, nextLevelExp - currentLevelExp));
    
    // Calculate streak (mock data for demo)
    setStreakDays(Math.floor(stats.todayCustomers / 5) + 1);
  }, [stats, level]);

  const dailyGoals: DailyGoal[] = [
    {
      id: "customers",
      title: "Serve 20 Customers",
      description: "Help 20 customers today",
      target: 20,
      current: stats.todayCustomers,
      points: 100,
      icon: Users,
      completed: stats.todayCustomers >= 20
    },
    {
      id: "efficiency",
      title: "90% Efficiency",
      description: "Maintain 90%+ queue efficiency",
      target: 90,
      current: stats.queueEfficiency,
      points: 150,
      icon: Zap,
      completed: stats.queueEfficiency >= 90
    },
    {
      id: "wait_time",
      title: "Quick Service",
      description: "Keep average wait under 10 minutes",
      target: 10,
      current: stats.avgWaitTime,
      points: 120,
      icon: Timer,
      completed: stats.avgWaitTime <= 10
    },
    {
      id: "completion",
      title: "Perfect Completion",
      description: "Achieve 95%+ completion rate",
      target: 95,
      current: stats.completionRate,
      points: 200,
      icon: CheckCircle2,
      completed: stats.completionRate >= 95
    }
  ];

  const achievements: Achievement[] = [
    {
      id: "speed_demon",
      title: "Speed Demon",
      description: "Serve customers in under 5 minutes average",
      icon: Zap,
      progress: Math.max(0, 10 - stats.avgWaitTime),
      maxProgress: 10,
      unlocked: stats.avgWaitTime <= 5,
      points: 500
    },
    {
      id: "customer_champion",
      title: "Customer Champion",
      description: "Serve 50 customers in a day",
      icon: Trophy,
      progress: stats.todayCustomers,
      maxProgress: 50,
      unlocked: stats.todayCustomers >= 50,
      points: 300
    },
    {
      id: "efficiency_expert",
      title: "Efficiency Expert",
      description: "Maintain 95%+ efficiency for a day",
      icon: Target,
      progress: stats.queueEfficiency,
      maxProgress: 100,
      unlocked: stats.queueEfficiency >= 95,
      points: 250
    },
    {
      id: "perfect_day",
      title: "Perfect Day",
      description: "100% completion rate with 0 cancellations",
      icon: Star,
      progress: stats.completionRate,
      maxProgress: 100,
      unlocked: stats.completionRate === 100,
      points: 400
    }
  ];

  const completedGoals = dailyGoals.filter(goal => goal.completed);
  const completedAchievements = achievements.filter(achievement => achievement.unlocked);
  const progressPercentage = (experiencePoints / 1000) * 100;

  return (
    <div className="space-y-6">
      {/* Player Profile */}
      <Card className="bg-gradient-to-r from-primary/10 to-accent/10 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-primary-foreground">
              <Trophy className="h-6 w-6" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <span>Queue Manager</span>
                <Badge className="bg-accent text-accent-foreground">Level {level}</Badge>
              </div>
              <div className="text-sm text-muted-foreground">{business.name}</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Experience Progress */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Experience</span>
              <span>{experiencePoints}/1000 XP</span>
            </div>
            <Progress value={progressPercentage} className="h-3" />
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{totalPoints}</div>
              <div className="text-xs text-muted-foreground">Total Points</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-accent">{level}</div>
              <div className="text-xs text-muted-foreground">Level</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success">{streakDays}</div>
              <div className="text-xs text-muted-foreground">Day Streak</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-warning">{completedAchievements.length}</div>
              <div className="text-xs text-muted-foreground">Achievements</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Daily Goals */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Daily Goals ({completedGoals.length}/{dailyGoals.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dailyGoals.map((goal, index) => (
              <motion.div
                key={goal.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`p-4 rounded-lg border transition-all ${
                  goal.completed 
                    ? 'bg-success/10 border-success/30' 
                    : 'bg-muted/30 border-border'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${
                      goal.completed ? 'bg-success text-success-foreground' : 'bg-muted text-muted-foreground'
                    }`}>
                      <goal.icon className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-semibold">{goal.title}</div>
                      <div className="text-sm text-muted-foreground">{goal.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={goal.completed ? "default" : "secondary"}
                      className="text-xs"
                    >
                      +{goal.points} pts
                    </Badge>
                    {goal.completed && (
                      <CheckCircle2 className="h-5 w-5 text-success" />
                    )}
                  </div>
                </div>
                
                {/* Progress Bar */}
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>
                      {goal.id === 'wait_time' 
                        ? `${goal.current.toFixed(1)} min (target: <${goal.target} min)`
                        : `${Math.min(goal.current, goal.target)}/${goal.target}`
                      }
                    </span>
                  </div>
                  <Progress 
                    value={
                      goal.id === 'wait_time'
                        ? Math.max(0, (goal.target - goal.current) / goal.target * 100)
                        : (goal.current / goal.target) * 100
                    } 
                    className={`h-2 ${goal.completed ? 'bg-success/20' : ''}`}
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Achievements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Achievements ({completedAchievements.length}/{achievements.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.id}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className={`p-4 rounded-lg border transition-all ${
                  achievement.unlocked 
                    ? 'bg-gradient-to-r from-warning/10 to-accent/10 border-warning/30 shadow-md' 
                    : 'bg-muted/20 border-border opacity-75'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className={`p-3 rounded-full ${
                      achievement.unlocked 
                        ? 'bg-warning text-warning-foreground' 
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      <achievement.icon className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="font-semibold">{achievement.title}</div>
                      <div className="text-sm text-muted-foreground">{achievement.description}</div>
                    </div>
                  </div>
                  {achievement.unlocked && (
                    <Badge className="bg-warning text-warning-foreground">
                      +{achievement.points} pts
                    </Badge>
                  )}
                </div>
                
                {!achievement.unlocked && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Progress</span>
                      <span>{Math.min(achievement.progress, achievement.maxProgress)}/{achievement.maxProgress}</span>
                    </div>
                    <Progress 
                      value={(achievement.progress / achievement.maxProgress) * 100} 
                      className="h-1"
                    />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Performance Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.queueEfficiency >= 90 && (
              <div className="flex items-center gap-3 p-3 bg-success/10 border border-success/20 rounded-lg">
                <Zap className="h-5 w-5 text-success" />
                <div>
                  <div className="font-semibold text-success">Excellent Efficiency!</div>
                  <div className="text-sm text-muted-foreground">
                    You're operating at {stats.queueEfficiency}% efficiency - keep it up!
                  </div>
                </div>
              </div>
            )}
            
            {stats.avgWaitTime <= 5 && (
              <div className="flex items-center gap-3 p-3 bg-primary/10 border border-primary/20 rounded-lg">
                <Clock className="h-5 w-5 text-primary" />
                <div>
                  <div className="font-semibold text-primary">Lightning Fast Service!</div>
                  <div className="text-sm text-muted-foreground">
                    Average wait time of {stats.avgWaitTime.toFixed(1)} minutes is exceptional!
                  </div>
                </div>
              </div>
            )}
            
            {completedGoals.length === dailyGoals.length && (
              <div className="flex items-center gap-3 p-3 bg-warning/10 border border-warning/20 rounded-lg">
                <Trophy className="h-5 w-5 text-warning" />
                <div>
                  <div className="font-semibold text-warning">All Daily Goals Complete!</div>
                  <div className="text-sm text-muted-foreground">
                    Amazing work today - you've completed all daily goals!
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GameficationDashboard;