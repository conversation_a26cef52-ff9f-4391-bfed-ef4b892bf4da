import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  MessageSquare, 
  Brain, 
  Shield, 
  BarChart3, 
  Clock, 
  Smartphone,
  Globe,
  TrendingUp,
  Users
} from "lucide-react";

const Features = () => {
  const features = [
    {
      icon: MessageSquare,
      title: "WhatsApp & SMS Integration",
      description: "Customers join queues using WhatsApp or SMS - no app downloads required. Works with any mobile phone.",
      badge: "Core Feature",
      color: "text-primary",
      bgColor: "bg-primary/10"
    },
    {
      icon: Brain,
      title: "AI Wait Time Prediction",
      description: "Machine learning algorithms predict wait times with ±3 minute accuracy based on historical patterns.",
      badge: "AI Powered",
      color: "text-accent",
      bgColor: "bg-accent/10"
    },
    {
      icon: Shield,
      title: "GDPR Compliant",
      description: "Built-in privacy protection with Irish data residency. Automatic data deletion and consent management.",
      badge: "Secure",
      color: "text-success",
      bgColor: "bg-success/10"
    },
    {
      icon: BarChart3,
      title: "Real-time Analytics",
      description: "Track customer flow, peak hours, and revenue impact. Get insights to optimize your business operations.",
      badge: "Analytics",
      color: "text-secondary",
      bgColor: "bg-secondary/10"
    },
    {
      icon: Clock,
      title: "5-Minute Setup",
      description: "Get started in minutes, not hours. Simple web dashboard works on any device - no training required.",
      badge: "Easy Setup",
      color: "text-warning",
      bgColor: "bg-warning/10"
    },
    {
      icon: Globe,
      title: "Multi-Language Support",
      description: "Supports English, Irish (Gaeilge), Polish, and Lithuanian - perfect for diverse Irish communities.",
      badge: "Inclusive",
      color: "text-primary",
      bgColor: "bg-primary/10"
    }
  ];

  const stats = [
    {
      icon: Users,
      value: "78%",
      label: "Reduction in customer complaints about waiting",
      color: "text-primary"
    },
    {
      icon: TrendingUp,
      value: "€2.4K",
      label: "Average annual savings per business",
      color: "text-success"
    },
    {
      icon: Clock,
      value: "23 min",
      label: "Daily time saved per employee",
      color: "text-accent"
    }
  ];

  return (
    <section className="section-padding bg-background">
      <div className="container mx-auto container-padding">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <Badge variant="secondary" className="mb-4">
            Features
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            Everything you need to{" "}
            <span className="text-gradient">transform</span>{" "}
            your queue management
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            QueueFlow combines cutting-edge AI with simple WhatsApp integration 
            to create the most user-friendly queue management system for Irish SMEs.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="card-hover border-0 shadow-md"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between mb-4">
                  <div className={`p-3 rounded-xl ${feature.bgColor}`}>
                    <feature.icon className={`h-6 w-6 ${feature.color}`} />
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {feature.badge}
                  </Badge>
                </div>
                <CardTitle className="text-xl font-semibold">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats Section */}
        <div className="bg-gradient-subtle rounded-2xl p-8 md:p-12">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Proven results for Irish businesses
            </h3>
            <p className="text-lg text-muted-foreground">
              Based on research across 300+ Irish SMEs
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {stats.map((stat, index) => (
              <div 
                key={index}
                className="text-center animate-scale-in"
                style={{ animationDelay: `${(index + 1) * 200}ms` }}
              >
                <div className={`inline-flex p-4 rounded-full bg-background shadow-md mb-4`}>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
                <div className={`text-4xl md:text-5xl font-bold mb-2 ${stat.color}`}>
                  {stat.value}
                </div>
                <p className="text-muted-foreground text-lg">
                  {stat.label}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;