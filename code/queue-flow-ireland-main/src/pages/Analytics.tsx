import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Clock, 
  Star, 
  Download,
  Calendar,
  Target,
  AlertCircle
} from "lucide-react";
import AppNavbar from "@/components/layout/AppNavbar";
import { PredictiveAnalytics } from "@/components/analytics/PredictiveAnalytics";
import { Business } from "@/lib/types";

const Analytics = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("7d");
  const [selectedBusiness, setSelectedBusiness] = useState("all");

  // Mock data for demonstration
  const mockBusiness: Business = {
    id: "1",
    name: "Elite Hair Salon",
    type: "salon",
    phone: "+353 87 123 4567",
    status: "open",
    staff_count: 3,
    avg_service_time: 45,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const stats = [
    {
      title: "Total Customers",
      value: "1,247",
      change: "+12.5%",
      changeType: "increase" as const,
      icon: Users,
      description: "Customers served this period"
    },
    {
      title: "Average Wait Time",
      value: "8.5 min",
      change: "-2.3%",
      changeType: "decrease" as const,
      icon: Clock,
      description: "Average time in queue"
    },
    {
      title: "Completion Rate",
      value: "96.2%",
      change: "****%",
      changeType: "increase" as const,
      icon: Target,
      description: "Successfully served customers"
    },
    {
      title: "Customer Satisfaction",
      value: "4.8/5.0",
      change: "+0.2",
      changeType: "increase" as const,
      icon: Star,
      description: "Average customer rating"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <AppNavbar />
      
      <div className="container mx-auto container-padding py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8 animate-fade-in-up">
            <Badge variant="secondary" className="mb-4">
              Business Analytics
            </Badge>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
                  Data-driven{" "}
                  <span className="text-gradient">insights</span>
                </h1>
                <p className="text-xl text-muted-foreground max-w-3xl">
                  Track performance, identify trends, and optimize your queue management with advanced analytics.
                </p>
              </div>
              <div className="flex items-center gap-4">
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="w-[140px]">
                    <Calendar className="h-4 w-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="24h">Last 24 hours</SelectItem>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Export
                </Button>
              </div>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <Card key={index} className="hover-scale">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                          <p className="text-2xl font-bold">{stat.value}</p>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">{stat.description}</span>
                      <span className={`text-xs font-medium flex items-center gap-1 ${
                        stat.changeType === 'increase' ? 'text-success' : 'text-primary'
                      }`}>
                        <TrendingUp className="h-3 w-3" />
                        {stat.change}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Analytics Tabs */}
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">
                <BarChart3 className="h-4 w-4 mr-2" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="performance">
                <TrendingUp className="h-4 w-4 mr-2" />
                Performance
              </TabsTrigger>
              <TabsTrigger value="customers">
                <Users className="h-4 w-4 mr-2" />
                Customers
              </TabsTrigger>
              <TabsTrigger value="predictions">
                <Target className="h-4 w-4 mr-2" />
                AI Predictions
              </TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Queue Activity Today</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 flex items-center justify-center text-muted-foreground">
                      <div className="text-center">
                        <BarChart3 className="h-12 w-12 mx-auto mb-3 opacity-50" />
                        <p>Interactive chart showing hourly queue activity</p>
                        <p className="text-sm">Peak times, wait durations, and customer flow</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Weekly Trends</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 flex items-center justify-center text-muted-foreground">
                      <div className="text-center">
                        <TrendingUp className="h-12 w-12 mx-auto mb-3 opacity-50" />
                        <p>7-day performance comparison</p>
                        <p className="text-sm">Customer volume, satisfaction, and efficiency trends</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Business Health Score</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-success mb-2">92/100</div>
                      <div className="text-sm text-muted-foreground">Overall Health</div>
                      <div className="mt-2 bg-success/20 text-success text-xs px-2 py-1 rounded">Excellent</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary mb-2">87/100</div>
                      <div className="text-sm text-muted-foreground">Efficiency</div>
                      <div className="mt-2 bg-primary/20 text-primary text-xs px-2 py-1 rounded">Very Good</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-warning mb-2">75/100</div>
                      <div className="text-sm text-muted-foreground">Capacity Usage</div>
                      <div className="mt-2 bg-warning/20 text-warning text-xs px-2 py-1 rounded">Good</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Performance Tab */}
            <TabsContent value="performance" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Service Time Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Average Service Time</span>
                        <span className="font-medium">32 minutes</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Fastest Service</span>
                        <span className="font-medium">15 minutes</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Longest Service</span>
                        <span className="font-medium">78 minutes</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Staff Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { name: "Sarah Johnson", customers: 45, rating: 4.9 },
                        { name: "Mike O'Brien", customers: 38, rating: 4.7 },
                        { name: "Emma Walsh", customers: 42, rating: 4.8 }
                      ].map((staff, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{staff.name}</div>
                            <div className="text-sm text-muted-foreground">{staff.customers} customers served</div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className="font-medium">{staff.rating}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Customers Tab */}
            <TabsContent value="customers" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Customer Demographics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">New Customers</span>
                        <span className="font-medium">23% (287)</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Returning Customers</span>
                        <span className="font-medium">77% (960)</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Peak Age Group</span>
                        <span className="font-medium">25-40 years</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Customer Journey</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Average Visits/Month</span>
                        <span className="font-medium">2.3</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Customer Retention</span>
                        <span className="font-medium">84%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Referral Rate</span>
                        <span className="font-medium">31%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Customer Feedback Highlights</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { comment: "Love the WhatsApp notifications! Makes it so convenient.", rating: 5, customer: "Mary K." },
                      { comment: "Short wait times and excellent service as always.", rating: 5, customer: "John D." },
                      { comment: "The online queue system is brilliant. No more waiting around!", rating: 5, customer: "Lisa M." },
                      { comment: "Great haircut and very professional staff.", rating: 4, customer: "Tom S." }
                    ].map((feedback, index) => (
                      <div key={index} className="p-4 border border-border rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex">
                            {Array.from({ length: feedback.rating }).map((_, i) => (
                              <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                            ))}
                          </div>
                          <span className="text-sm text-muted-foreground">{feedback.customer}</span>
                        </div>
                        <p className="text-sm">{feedback.comment}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* AI Predictions Tab */}
            <TabsContent value="predictions" className="space-y-6">
              <PredictiveAnalytics business={mockBusiness} stats={{
                todayCustomers: 47,
                avgWaitTime: 8.5,
                completionRate: 96.2,
                queueEfficiency: 87
              }} />
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <AlertCircle className="h-5 w-5 text-warning" />
                      Optimization Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { title: "Staff Scheduling", description: "Add 1 staff member on Fridays 2-4 PM to reduce wait times", impact: "High", type: "warning" },
                        { title: "Service Duration", description: "Hair wash service taking 15% longer than optimal", impact: "Medium", type: "info" },
                        { title: "Peak Hours", description: "Consider offering discounts for off-peak appointments", impact: "Low", type: "success" }
                      ].map((rec, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 border border-border rounded-lg">
                          <div className={`w-2 h-2 rounded-full mt-2 ${
                            rec.type === 'warning' ? 'bg-warning' : 
                            rec.type === 'info' ? 'bg-primary' : 'bg-success'
                          }`}></div>
                          <div className="flex-1">
                            <div className="font-medium">{rec.title}</div>
                            <div className="text-sm text-muted-foreground">{rec.description}</div>
                            <div className="mt-1">
                              <Badge variant="outline" className="text-xs">
                                {rec.impact} Impact
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Demand Forecast</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary mb-2">Expected Tomorrow</div>
                        <div className="text-4xl font-bold">47 customers</div>
                        <div className="text-sm text-muted-foreground">+12% vs last Tuesday</div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Morning (9AM-12PM)</span>
                          <span className="font-medium">15 customers</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Afternoon (12PM-5PM)</span>
                          <span className="font-medium">22 customers</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Evening (5PM-8PM)</span>
                          <span className="font-medium">10 customers</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Analytics;