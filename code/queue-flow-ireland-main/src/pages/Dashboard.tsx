import { useState, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Calendar, QrCode, Settings, BarChart3, Users, Bell, Trophy, Shield } from "lucide-react";
import { Business } from "@/lib/types";
import BusinessSelector from "@/components/dashboard/BusinessSelector";
import EnhancedQueueManager from "@/components/dashboard/EnhancedQueueManager";
import AnalyticsDashboard from "@/components/dashboard/AnalyticsDashboard";
import QRCodeGenerator from "@/components/customer/QRCodeGenerator";
import BusinessSettings from "@/components/dashboard/BusinessSettings";
import RealTimeNotifications from "@/components/dashboard/RealTimeNotifications";
import CustomerManagement from "@/components/dashboard/CustomerManagement";
import GameficationDashboard from "@/components/engagement/GameficationDashboard";
import AdminDashboard from "@/components/admin/AdminDashboard";

const Dashboard = () => {
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);
  const [analyticsKey, setAnalyticsKey] = useState(0);
  const [isAdmin, setIsAdmin] = useState(false); // Mock admin check - replace with real auth

  // Force analytics refresh when queue changes
  const handleAnalyticsUpdate = useCallback(() => {
    setAnalyticsKey(prev => prev + 1);
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto container-padding py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-12 animate-fade-in-up">
            <Badge variant="secondary" className="mb-4">
              Business Dashboard
            </Badge>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Manage your queue with{" "}
              <span className="text-gradient">real-time insights</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl">
              Simple, powerful dashboard that works on any device. Monitor your queue, 
              serve customers, and track performance - all in one place.
            </p>
          </div>

          {/* Business Selector */}
          <div className="mb-8">
            <BusinessSelector 
              selectedBusiness={selectedBusiness}
              onBusinessChange={setSelectedBusiness}
            />
          </div>

          {/* Dashboard Content */}
          {selectedBusiness ? (
            <div className="bg-gradient-subtle rounded-2xl p-6 md:p-8 shadow-xl">
              {/* Dashboard Header */}
              <div className="flex items-center justify-between mb-8">
                <div>
                  <h2 className="text-2xl font-bold mb-2">{selectedBusiness.name}</h2>
                  <p className="text-muted-foreground flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {new Date().toLocaleDateString('en-IE', { 
                      weekday: 'long', 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}
                    <span>•</span>
                    {new Date().toLocaleTimeString('en-IE', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>
                <div className="flex items-center gap-4">
                  <Badge variant="outline" className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      selectedBusiness.status === 'open' ? 'bg-success' :
                      selectedBusiness.status === 'busy' ? 'bg-warning' :
                      selectedBusiness.status === 'paused' ? 'bg-muted' :
                      'bg-destructive'
                    } animate-pulse`}></div>
                    <span className="capitalize">{selectedBusiness.status}</span>
                  </Badge>
                </div>
              </div>

              {/* Admin Toggle */}
              <div className="mb-4">
                <Badge 
                  variant={isAdmin ? "default" : "outline"} 
                  className="cursor-pointer"
                  onClick={() => setIsAdmin(!isAdmin)}
                >
                  <Shield className="h-3 w-3 mr-1" />
                  {isAdmin ? "Admin Mode" : "Business Mode"}
                </Badge>
              </div>

              {/* Dashboard Content */}
              {isAdmin ? (
                <AdminDashboard />
              ) : (
                <>
                  {/* Dashboard Tabs */}
                  <Tabs defaultValue="queue" className="space-y-6">
                    <TabsList className="grid w-full grid-cols-7 lg:w-[700px]">
                      <TabsTrigger value="queue" className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Queue
                      </TabsTrigger>
                      <TabsTrigger value="analytics" className="flex items-center gap-2">
                        <BarChart3 className="h-4 w-4" />
                        Analytics
                      </TabsTrigger>
                      <TabsTrigger value="customers" className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Customers
                      </TabsTrigger>
                      <TabsTrigger value="notifications" className="flex items-center gap-2">
                        <Bell className="h-4 w-4" />
                        Notifications
                      </TabsTrigger>
                      <TabsTrigger value="gamification" className="flex items-center gap-2">
                        <Trophy className="h-4 w-4" />
                        Goals
                      </TabsTrigger>
                      <TabsTrigger value="qrcode" className="flex items-center gap-2">
                        <QrCode className="h-4 w-4" />
                        QR Code
                      </TabsTrigger>
                      <TabsTrigger value="settings" className="flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Settings
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="queue" className="space-y-6">
                      <EnhancedQueueManager 
                        business={selectedBusiness} 
                        onAnalyticsUpdate={handleAnalyticsUpdate}
                      />
                    </TabsContent>

                    <TabsContent value="analytics" className="space-y-6">
                      <AnalyticsDashboard 
                        key={analyticsKey} 
                        business={selectedBusiness} 
                      />
                    </TabsContent>

                    <TabsContent value="customers" className="space-y-6">
                      <CustomerManagement business={selectedBusiness} />
                    </TabsContent>

                    <TabsContent value="notifications" className="space-y-6">
                      <RealTimeNotifications 
                        queue={[]} 
                        businessName={selectedBusiness.name}
                      />
                    </TabsContent>

                    <TabsContent value="gamification" className="space-y-6">
                      <GameficationDashboard 
                        business={selectedBusiness}
                        stats={{
                          todayCustomers: 0,
                          avgWaitTime: 0,
                          completionRate: 100,
                          queueEfficiency: 100
                        }}
                      />
                    </TabsContent>

                    <TabsContent value="qrcode" className="space-y-6">
                      <QRCodeGenerator business={selectedBusiness} />
                    </TabsContent>

                    <TabsContent value="settings" className="space-y-6">
                      <BusinessSettings business={selectedBusiness} />
                    </TabsContent>
                  </Tabs>
                </>
              )}
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <Users className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-xl font-semibold mb-2">Select or Create a Business</h3>
              <p>Choose an existing business or create a new one to start managing your queue.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;